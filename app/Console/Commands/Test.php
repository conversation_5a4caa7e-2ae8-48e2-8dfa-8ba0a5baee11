<?php

namespace App\Console\Commands;

use App\Services\AppsService;
use App\Services\Business\Google\GoogleStorageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Weaviate\Weaviate;

class Test extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->testGCS();
        return Command::SUCCESS;
    }

    private  function binary($n, $arr,$true_value = 1,$false_value = 0) {
        if ($n == 0) {
            echo implode("", $arr) . "\
";
        } else {
            $arr[$n-1] = $false_value;
            $this->binary($n-1, $arr,$true_value,$false_value);
            $arr[$n-1] = $true_value;
            $this->binary($n-1, $arr,$true_value,$false_value);
        }
    }
    private function test(){
        $groups = [
            $this->groupObject('版本', 'b.app_version', 'app_version', 'All'),
            $this->groupObject('国家', 'b.country', 'country', 'All'),
            $this->groupObject('来源', 'a.source', 'source', -1),
            $this->groupObject('动作', 'a.action', 'action', -1),
        ];

        $n = 4;
        $arr = array_fill(0, $n, 0);
//        $this->binary($n, $arr,['key'=>'_ALL','desc'=>'所有'.,'group_by'=>'','select'=>],'_NO');
    }

    public function testCache(){
        for ($i = 1; $i<=100;$i++){
            $this->concurrenceChecker(5,10);
            var_dump('i='.$i);
        }
    }

    public function concurrenceChecker($count = 5, $second = 10)
    {
        $key = 'big_query_handle';
        $count = Cache::store('database')->get($key, 0);
        Cache::store('database')->put($key, $count + 1, $second);
        $locked = true;
        while ($locked) {
            if ($count < 3) {
                // 执行任务的代码
                $locked = false;
            } else {
                sleep($second); // 等待 1 秒钟
                $locked = false;
            }
        }
    }

    public function testAIDatabase(){
//        var_dump(json_decode('{"driver":"mysql","url":null,"host":"swan-web.cxoi2ljqdaed.us-west-2.rds.amazonaws.com","port":"3306","database":"swan_web","username":"swan_web_pro","password":"ySfBknnphKR30PfHBS4JF4S9R","unix_socket":"","charset":"utf8mb4","collation":"utf8mb4_unicode_ci","prefix":"","prefix_indexes":true,"strict":false,"engine":null,"options":[]}'));
//        var_dump();json_encode(Config::get('database.connections.mysql'));
//        DB::connection('ai')
//        $data = DB::connection('ai')->table('ai_base_app')->where('id',1)->first();
        $config = json_encode(DB::connection('ai')->getConfig());
        var_dump($config);
        $config = json_decode($config,true);
        var_dump($config);exit;
        DB::connection('ai')->getConfig();
        DB::connection('ai')->getDatabaseName();
        var_dump(Config::get('database.connections.mysql'));exit;
    }

    public function testWeaviate(){
        $weaviate = new Weaviate('http://************:7774/v1/', '',['X-OpenAI-Api-Key'=>'***************************************************']);
//        var_dump($weaviate->objects()->get());
//        $weaviate->objects()->create()
      $data =  $weaviate->graphql()->get("{
    Get {
      Article(where: {
          path: [\"wordCount\"],
          operator: GreaterThan,
          valueInt: 1000
        }) {
        title
      }
    }
  }");
      var_dump($data);
    }

    public function testGCS(){
        $apps = AppsService::getGCSAppsByMachine();
        $google_play_service = new GoogleStorageService();
        foreach ($apps as $app){
            $project_id = $app->project_id;
            $file_path = $app->file_path;
            $developer_id = $app->developer_id;
            $google_play_service->downloadGooglePlaySalesReport($project_id,$file_path,$developer_id);
        }
    }

}
