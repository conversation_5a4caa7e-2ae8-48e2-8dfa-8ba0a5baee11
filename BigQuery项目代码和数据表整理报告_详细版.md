# BigQuery项目代码和数据表整理报告（详细版）

## 1. 数据来源分析

### 1.1 数据源
项目中的数据主要来源于Firebase Analytics，通过Google BigQuery进行查询和处理。Firebase Analytics收集了移动应用中的用户行为数据，以事件的形式存储。

### 1.2 数据表结构
BigQuery中的数据表结构如下：
```
`project_id.analytics_app_id.events_*`
```
- `project_id`: Google Cloud项目ID
- `analytics_app_id`: Firebase Analytics应用ID
- `events_*`: 事件表，支持通配符查询
  - `events_20231201`: 当日完整数据
  - `events_intraday_20231201`: 当日实时数据（未完整）

### 1.3 核心事件类型
从代码中可以看出，系统主要处理以下几类事件：

1. **用户生命周期事件**:
   - `add_his`: 新装用户标识事件
   - `add_install`: 整装用户标识事件
   - `act_1`: 主要活跃事件
   - `act_srv_1`: 服务活跃事件

2. **广告相关事件**:
   - `ad_conversion_funnel`: 广告转化漏斗事件
   - `launcher_ad_scene_funnel`: 启动器广告场景漏斗事件

3. **功能使用事件**:
   - 天气应用特有事件: `weather_add_city`

## 2. 应用类型与映射关系

### 2.1 应用类型定义
系统中定义了多种应用类型，用于区分不同业务线的应用：

```php
const BQ_TYPE_V1 = 1; //合作FF(驰豹)
const BQ_TYPE_V2 = 2; //自营清理
const BQ_TYPE_V3 = 3; //自营个性化（launcher）
const BQ_TYPE_V4 = 4; //合作猎户
const BQ_TYPE_V7 = 7; //自营天气
```

### 2.2 应用与BigQuery的映射关系
应用与BigQuery项目的映射关系存储在数据库表中：

1. **s_apps表**: 存储应用基本信息
   - `mid`: 机器ID
   - `bq_type`: 应用类型
   - `ip`: 服务器IP
   - `status`: 状态

2. **s_big_query_auth表**: 存储BigQuery认证信息
   - `app_id`: 应用ID
   - `project_id`: BigQuery项目ID
   - `file_path`: 认证文件路径
   - `mid`: 机器ID
   - `status`: 状态

通过`AppsService`类中的方法，系统可以根据应用类型和服务器IP获取对应的BigQuery配置。

## 3. 核心服务类与数据表映射

### 3.1 BigQueryBaseService
**文件位置**: `app/Services/BigQuery/BigQueryBaseService.php`

**主要功能**:
- 执行BigQuery查询
- 处理查询结果
- 将数据导入MySQL

**读取的数据表**:
- `s_big_query_task`: 查询任务状态

**写入的数据表**:
- 由具体任务配置决定，通过`table_info['table_name']`参数指定
- 更新任务状态到`s_big_query_task`表

### 3.2 BigQueryTaskService
**文件位置**: `app/Services/BigQuery/BigQueryTaskService.php`

**主要功能**:
- 管理BigQuery查询任务

**读取的数据表**:
- `s_big_query_task`: 查询任务记录

**写入的数据表**:
- `s_big_query_task`: 创建和更新任务记录

### 3.3 DataHandleService
**文件位置**: `app/Services/BigQuery/DataHandleService.php`

**主要功能**:
- 通用数据处理逻辑
- 生成功能场景报表
- 生成三维数据报表
- 生成商业场景报表

**读取的数据表**:
- 基础PV数据表（由具体业务类型决定）

**写入的数据表**:
- `s_report_func_scene`: 功能场景报表
- `s_report_three_dimensional`: 三维数据报表
- `s_report_business_scene_*`: 商业场景报表（根据业务类型有不同后缀）

### 3.4 DataHandleSelfBusinessService
**文件位置**: `app/Services/BigQuery/DataHandleSelfBusinessService.php`

**主要功能**:
- 处理自营清理类应用的业务数据

**读取的数据表**:
- `s_big_query_base_pv_self_business_*`: 自营清理类应用的PV数据
- `s_big_query_base_uv`: 用户数据

**写入的数据表**:
- `s_report_business_scene_self`: 自营清理类应用的商业场景报表

### 3.5 DataHandleSelfIndividuationBusinessService
**文件位置**: `app/Services/BigQuery/DataHandleSelfIndividuationBusinessService.php`

**主要功能**:
- 处理自营个性化类应用的业务数据

**读取的数据表**:
- `s_big_query_base_pv_self_individuation_business_*`: 自营个性化类应用的PV数据
- `s_big_query_base_uv`: 用户数据
- `s_big_query_base_subscribe_uv`: 订阅用户数据

**写入的数据表**:
- `s_report_business_scene_self_individuation`: 自营个性化类应用的商业场景报表
- `s_big_query_func_chat`: 聊天功能数据

### 3.6 DataHandleSelfService
**文件位置**: `app/Services/BigQuery/DataHandleSelfService.php`

**主要功能**:
- 处理自营应用的通用数据

**读取的数据表**:
- `s_big_query_base_uv`: 用户数据
- `s_big_query_base_pv_*`: PV数据表

**写入的数据表**:
- `s_report_three_dimensional`: 三维数据报表

### 3.7 CooperationOrion/DataHandleBusinessService
**文件位置**: `app/Services/BigQuery/CooperationOrion/DataHandleBusinessService.php`

**主要功能**:
- 处理合作猎户类应用的业务数据

**读取的数据表**:
- `s_big_query_base_pv_cooperation_orion_business_*`: 合作猎户类应用的PV数据
- `s_big_query_base_uv`: 用户数据

**写入的数据表**:
- `s_report_business_scene_cooperation_orion`: 合作猎户类应用的商业场景报表

### 3.8 SelfWeather/DataHandleSelfWeatherBusinessService
**文件位置**: `app/Services/BigQuery/SelfWeather/DataHandleSelfWeatherBusinessService.php`

**主要功能**:
- 处理自营天气类应用的业务数据

**读取的数据表**:
- `s_big_query_base_pv_self_weather_business_*`: 自营天气类应用的PV数据
- `s_big_query_base_uv`: 用户数据

**写入的数据表**:
- `s_report_self_weather_business_scene`: 自营天气类应用的商业场景报表

## 4. 命令行任务与数据表映射

### 4.1 BigQueryBaseCommand
**文件位置**: `app/Console/BigQueryBaseCommand.php`

**主要功能**:
- 提供BigQuery命令的基础功能
- 获取项目ID列表
- 生成组合查询

**读取的数据表**:
- `s_big_query_auth`: 获取BigQuery认证信息

**写入的数据表**:
- 无直接写入，由子类实现

### 4.2 BaseUv
**文件位置**: `app/Console/Commands/BigQuery/BaseUv.php`

**主要功能**:
- 查询用户基础数据

**读取的数据表**:
- BigQuery: `project_id.analytics_app_id.events_*`

**写入的数据表**:
- `s_big_query_base_uv`: 用户基础数据
- `s_big_query_task`: 任务记录

### 4.3 SelfClean/BasePvBusiness
**文件位置**: `app/Console/Commands/BigQuery/SelfClean/BasePvBusiness.php`

**主要功能**:
- 查询自营清理类应用的事件PV数据

**读取的数据表**:
- BigQuery: `project_id.analytics_app_id.events_*`

**写入的数据表**:
- `s_big_query_base_pv_self_business_YYYYMM`: 自营清理类应用的PV数据
- `s_big_query_task`: 任务记录

### 4.4 SelfClean/DataHandleBusiness
**文件位置**: `app/Console/Commands/BigQuery/SelfClean/DataHandleBusiness.php`

**主要功能**:
- 处理自营清理类应用的业务数据

**读取的数据表**:
- `s_big_query_base_uv`: 用户数据
- `s_big_query_base_pv_self_business_YYYYMM`: 自营清理类应用的PV数据

**写入的数据表**:
- `s_report_business_scene_self`: 自营清理类应用的商业场景报表
- `s_report_three_dimensional`: 三维数据报表

### 4.5 SelfIndividuation/BasePvBusiness
**文件位置**: `app/Console/Commands/BigQuery/SelfIndividuation/BasePvBusiness.php`

**主要功能**:
- 查询自营个性化类应用的事件PV数据

**读取的数据表**:
- BigQuery: `project_id.analytics_app_id.events_*`

**写入的数据表**:
- `s_big_query_base_pv_self_individuation_business_YYYYMM`: 自营个性化类应用的PV数据
- `s_big_query_task`: 任务记录

### 4.6 SelfIndividuation/DataHandleBusiness
**文件位置**: `app/Console/Commands/BigQuery/SelfIndividuation/DataHandleBusiness.php`

**主要功能**:
- 处理自营个性化类应用的业务数据

**读取的数据表**:
- `s_big_query_base_uv`: 用户数据
- `s_big_query_base_pv_self_individuation_business_YYYYMM`: 自营个性化类应用的PV数据

**写入的数据表**:
- `s_report_business_scene_self_individuation`: 自营个性化类应用的商业场景报表
- `s_report_three_dimensional`: 三维数据报表

### 4.7 SelfIndividuation/BaseSubscribeUv
**文件位置**: `app/Console/Commands/BigQuery/SelfIndividuation/BaseSubscribeUv.php`

**主要功能**:
- 查询自营个性化类应用的订阅用户数据

**读取的数据表**:
- BigQuery: `project_id.analytics_app_id.events_*`

**写入的数据表**:
- `s_big_query_base_subscribe_uv`: 订阅用户数据
- `s_big_query_task`: 任务记录

### 4.8 SelfIndividuation/ChatFuncData
**文件位置**: `app/Console/Commands/BigQuery/SelfIndividuation/ChatFuncData.php`

**主要功能**:
- 查询自营个性化类应用的聊天功能数据

**读取的数据表**:
- BigQuery: `project_id.analytics_app_id.events_*`

**写入的数据表**:
- `s_big_query_func_chat`: 聊天功能数据
- `s_big_query_task`: 任务记录

### 4.9 CooperationOrion/BasePvBusiness
**文件位置**: `app/Console/Commands/BigQuery/CooperationOrion/BasePvBusiness.php`

**主要功能**:
- 查询合作猎户类应用的事件PV数据

**读取的数据表**:
- BigQuery: `project_id.analytics_app_id.events_*`

**写入的数据表**:
- `s_big_query_base_pv_cooperation_orion_business_YYYYMM`: 合作猎户类应用的PV数据
- `s_big_query_task`: 任务记录

### 4.10 CooperationOrion/DataHandleBusiness
**文件位置**: `app/Console/Commands/BigQuery/CooperationOrion/DataHandleBusiness.php`

**主要功能**:
- 处理合作猎户类应用的业务数据

**读取的数据表**:
- `s_big_query_base_uv`: 用户数据
- `s_big_query_base_pv_cooperation_orion_business_YYYYMM`: 合作猎户类应用的PV数据

**写入的数据表**:
- `s_report_business_scene_cooperation_orion`: 合作猎户类应用的商业场景报表
- `s_report_three_dimensional`: 三维数据报表

### 4.11 SelfWeather/BasePvBusiness
**文件位置**: `app/Console/Commands/BigQuery/SelfWeather/BasePvBusiness.php`

**主要功能**:
- 查询自营天气类应用的事件PV数据

**读取的数据表**:
- BigQuery: `project_id.analytics_app_id.events_*`

**写入的数据表**:
- `s_big_query_base_pv_self_weather_business_YYYYMM`: 自营天气类应用的PV数据
- `s_big_query_task`: 任务记录

### 4.12 SelfWeather/DataHandleBusiness
**文件位置**: `app/Console/Commands/BigQuery/SelfWeather/DataHandleBusiness.php`

**主要功能**:
- 处理自营天气类应用的业务数据

**读取的数据表**:
- `s_big_query_base_uv`: 用户数据
- `s_big_query_base_pv_self_weather_business_YYYYMM`: 自营天气类应用的PV数据

**写入的数据表**:
- `s_report_self_weather_business_scene`: 自营天气类应用的商业场景报表
- `s_report_three_dimensional`: 三维数据报表

### 4.13 BusinessDnuRetention
**文件位置**: `app/Console/Commands/BigQuery/BusinessDnuRetention.php`

**主要功能**:
- 查询新用户留存数据

**读取的数据表**:
- BigQuery: `project_id.analytics_app_id.events_*`

**写入的数据表**:
- `s_report_dnu_retention`: 新用户留存报表
- `s_big_query_task`: 任务记录

## 5. 业务类型与数据表映射详细对照

### 5.1 自营清理类应用 (BQ_TYPE_V2)

#### 5.1.1 数据流向
1. **数据源**: Firebase Analytics → BigQuery
2. **基础数据查询**:
   - `BaseUv` 命令查询用户数据 → 写入 `s_big_query_base_uv`
   - `SelfClean/BasePvBusiness` 命令查询PV数据 → 写入 `s_big_query_base_pv_self_business_YYYYMM`
3. **数据处理**:
   - `SelfClean/DataHandleBusiness` 命令处理数据
   - 调用 `DataHandleSelfBusinessService` 服务
4. **报表生成**:
   - 商业场景报表 → 写入 `s_report_business_scene_self`
   - 三维数据报表 → 写入 `s_report_three_dimensional`

#### 5.1.2 主要事件
- `ad_conversion_funnel`: 广告转化漏斗事件

#### 5.1.3 场景类型
- `SCENE_OPEN_SCREEN`: 开屏（来源于桌面icon）
- `SCENE_OPEN_SCREEN_EMEGENCY`: 开屏（来源于通知栏）
- `SCENE_RESULT_NATIVE`: 结果页大卡
- `SCENE_RESULT_INTERSTITIAL`: 结果页插屏
- `SCENE_RESULT_NATIVE_COLD`: 结果页大卡冷却期
- `SCENE_RESULT_INTERSTITIAL_COLD`: 结果页插屏冷却期

### 5.2 自营个性化类应用 (BQ_TYPE_V3)

#### 5.2.1 数据流向
1. **数据源**: Firebase Analytics → BigQuery
2. **基础数据查询**:
   - `BaseUv` 命令查询用户数据 → 写入 `s_big_query_base_uv`
   - `SelfIndividuation/BasePvBusiness` 命令查询PV数据 → 写入 `s_big_query_base_pv_self_individuation_business_YYYYMM`
   - `SelfIndividuation/BaseSubscribeUv` 命令查询订阅用户数据 → 写入 `s_big_query_base_subscribe_uv`
   - `SelfIndividuation/ChatFuncData` 命令查询聊天功能数据 → 写入 `s_big_query_func_chat`
3. **数据处理**:
   - `SelfIndividuation/DataHandleBusiness` 命令处理数据
   - 调用 `DataHandleSelfIndividuationBusinessService` 服务
4. **报表生成**:
   - 商业场景报表 → 写入 `s_report_business_scene_self_individuation`
   - 三维数据报表 → 写入 `s_report_three_dimensional`

#### 5.2.2 主要事件
- `launcher_ad_scene_funnel`: 启动器广告场景漏斗事件

#### 5.2.3 特有功能
- 订阅用户数据处理
- 聊天功能数据处理
- 启动器功能数据处理

### 5.3 合作猎户类应用 (BQ_TYPE_V4)

#### 5.3.1 数据流向
1. **数据源**: Firebase Analytics → BigQuery
2. **基础数据查询**:
   - `BaseUv` 命令查询用户数据 → 写入 `s_big_query_base_uv`
   - `CooperationOrion/BasePvBusiness` 命令查询PV数据 → 写入 `s_big_query_base_pv_cooperation_orion_business_YYYYMM`
3. **数据处理**:
   - `CooperationOrion/DataHandleBusiness` 命令处理数据
   - 调用 `CooperationOrion/DataHandleBusinessService` 服务
4. **报表生成**:
   - 商业场景报表 → 写入 `s_report_business_scene_cooperation_orion`
   - 三维数据报表 → 写入 `s_report_three_dimensional`

#### 5.3.2 主要事件
- 与自营清理类应用类似，处理广告转化漏斗事件

### 5.4 自营天气类应用 (BQ_TYPE_V7)

#### 5.4.1 数据流向
1. **数据源**: Firebase Analytics → BigQuery
2. **基础数据查询**:
   - `BaseUv` 命令查询用户数据 → 写入 `s_big_query_base_uv`
   - `SelfWeather/BasePvBusiness` 命令查询PV数据 → 写入 `s_big_query_base_pv_self_weather_business_YYYYMM`
3. **数据处理**:
   - `SelfWeather/DataHandleBusiness` 命令处理数据
   - 调用 `SelfWeather/DataHandleSelfWeatherBusinessService` 服务
4. **报表生成**:
   - 商业场景报表 → 写入 `s_report_self_weather_business_scene`
   - 三维数据报表 → 写入 `s_report_three_dimensional`

#### 5.4.2 主要事件
- 常规广告事件
- 天气特有事件: `weather_add_city`

#### 5.4.3 特殊处理
- 排除特定应用ID: `402892485`, `414248855`
- 主要活跃事件定义不同: `act_1` 或 `weather_add_city`

## 6. 数据表结构详细说明

### 6.1 基础数据表

#### 6.1.1 s_big_query_base_uv
存储用户基础数据，包括新用户、活跃用户等。

**主要字段**:
- `date`: 数据日期
- `app_id`: 应用ID
- `app_version`: 应用版本
- `country`: 国家
- `type`: 用户类型
- `act_srv_uv`: 服务活跃用户数
- `act_main_uv`: 主要活跃用户数

#### 6.1.2 s_big_query_base_pv_*_YYYYMM
存储事件PV数据，按业务类型和月份分表。

**主要字段**:
- `date`: 数据日期
- `app_id`: 应用ID
- `app_version`: 应用版本
- `country`: 国家
- `event_name`: 事件名称
- `type`: 用户类型
- `action`: 行为类型
- `pv`: 页面浏览量
- 其他事件特有字段（如`if_standbyid`, `cooling_period`, `ad_type`等）

#### 6.1.3 s_big_query_base_subscribe_uv
存储订阅用户数据。

**主要字段**:
- `date`: 数据日期
- `app_id`: 应用ID
- `type`: 类型
- `app_version`: 应用版本
- `country`: 国家
- `source`: 来源
- `action`: 行为
- `uv`: 用户数

### 6.2 报表数据表

#### 6.2.1 s_report_business_scene_*
存储商业场景报表数据，按业务类型分表。

**主要字段**:
- `date`: 数据日期
- `app_id`: 应用ID
- `app_version`: 应用版本
- `country`: 国家
- `user_type`: 用户类型
- `scene`: 场景类型
- `ad_startfun_pv`: 广告开始PV
- `ad_request_pv`: 广告请求PV
- `ad_return_pv`: 广告返回PV
- `ad_ready_pv`: 广告准备PV
- `ad_show`: 广告展示PV
- `ad_click`: 广告点击PV
- 各种比率和平均值字段

#### 6.2.2 s_report_three_dimensional
存储三维数据报表。

**主要字段**:
- `date`: 数据日期
- `app_id`: 应用ID
- `app_version`: 应用版本
- `country`: 国家
- `dnu`: 新用户数
- `act_srv_uv`: 服务活跃用户数
- `act_main_uv`: 主要活跃用户数
- `act_srv_pv`: 服务活跃PV
- `act_main_pv`: 主要活跃PV
- 各种比率和平均值字段

#### 6.2.3 s_report_dnu_retention
存储新用户留存报表。

**主要字段**:
- `date`: 数据日期
- `app_id`: 应用ID
- `app_version`: 应用版本
- `country`: 国家
- `android_version`: Android版本
- `dnu`: 新用户数
- `dnu_1`: 次日留存用户数
- `dnu_2`: 第3日留存用户数
- 其他留存数据和比率字段

#### 6.2.4 s_big_query_func_chat
存储聊天功能数据。

**主要字段**:
- `date`: 数据日期
- `app_id`: 应用ID
- `app_version`: 应用版本
- `country`: 国家
- `uv_new`: 新用户数
- `uv_act`: 活跃用户数
- `uv_def`: 默认用户数
- `uv_new_def`: 新默认用户数

### 6.3 任务管理表

#### 6.3.1 s_big_query_task
存储BigQuery查询任务记录。

**主要字段**:
- `date`: 数据日期
- `project_id`: BigQuery项目ID
- `version`: 任务版本号
- `title`: 任务标题
- `sql`: 执行的SQL语句
- `md5`: SQL的MD5值
- `desc`: 任务描述
- `status`: 任务状态
- `job_id`: BigQuery作业ID
- `total_bytes`: 处理的字节数
- `count`: 结果行数
- `response`: 响应信息

#### 6.3.2 s_big_query_auth
存储BigQuery认证信息。

**主要字段**:
- `app_id`: 应用ID
- `project_id`: BigQuery项目ID
- `file_path`: 认证文件路径
- `mid`: 机器ID
- `ip`: 服务器IP
- `status`: 状态

## 7. 总结

该项目是一个基于Laravel框架的数据分析系统，通过定时任务从BigQuery查询Firebase Analytics数据，处理后存储到MySQL数据库中，为业务报表提供支持。系统按照不同的应用类型(自营清理、自营个性化、合作猎户、自营天气)分别处理数据，并生成相应的业务报表。

每种业务类型都有其特定的数据处理流程和表结构，但整体遵循"数据查询→基础数据存储→数据处理→报表生成"的流程。系统通过任务管理机制确保数据处理的可靠性和可追踪性。