# BigQuery代码方法整理文档

## 1. BigQuery数据来源详细分析

### 1.1 数据源概述

BigQuery中的数据主要来源于**Firebase Analytics (Google Analytics for Firebase)**，这是Google提供的移动应用分析服务。数据以事件流的形式实时收集用户在移动应用中的行为数据。

### 1.2 数据表结构

#### 1.2.1 基础表结构
```sql
`project_id.analytics_app_id.events_*`
```

**表命名规则**：
- `project_id`: Google Cloud项目ID（如：`longwu-3965`、`phonecleaner-51870`）
- `analytics_app_id`: Firebase Analytics应用ID（如：`314732337`、`307936324`）
- `events_*`: 事件表，支持通配符查询
  - `events_20231201`: 当日完整数据
  - `events_intraday_20231201`: 当日实时数据（未完整）

#### 1.2.2 表分区策略
```sql
-- 查询当日数据（包含实时和完整数据）
WHERE (_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')

-- 查询多日数据
WHERE REGEXP_EXTRACT(_TABLE_SUFFIX, r'[0-9]+') BETWEEN '20231201' AND '20231207'
```

### 1.3 Firebase Analytics事件表核心字段

#### 1.3.1 用户标识字段
```sql
user_pseudo_id          -- 用户伪ID，用于标识唯一用户
user_id                 -- 用户真实ID（如果设置）
```

#### 1.3.2 事件基础字段
```sql
event_date              -- 事件日期 (YYYYMMDD格式)
event_timestamp         -- 事件时间戳（微秒级）
event_name              -- 事件名称
event_bundle_sequence_id -- 事件包序列ID
event_server_timestamp_offset -- 服务器时间偏移
```

#### 1.3.3 地理位置字段
```sql
geo.continent           -- 大洲
geo.country             -- 国家代码
geo.region              -- 地区
geo.city                -- 城市
geo.sub_continent       -- 次大洲
geo.metro               -- 都市区
```

#### 1.3.4 应用信息字段
```sql
app_info.id             -- 应用ID
app_info.version        -- 应用版本
app_info.install_store  -- 安装来源商店
app_info.firebase_app_id -- Firebase应用ID
app_info.install_source -- 安装来源
```

#### 1.3.5 设备信息字段
```sql
device.category                    -- 设备类别（mobile/tablet/desktop）
device.mobile_brand_name          -- 移动设备品牌
device.mobile_model_name          -- 移动设备型号
device.mobile_marketing_name      -- 移动设备营销名称
device.mobile_os_hardware_model   -- 移动设备硬件型号
device.operating_system           -- 操作系统
device.operating_system_version   -- 操作系统版本
device.vendor_id                  -- 供应商ID
device.advertising_id             -- 广告ID
device.language                   -- 设备语言
device.is_limited_ad_tracking     -- 是否限制广告跟踪
device.time_zone_offset_seconds   -- 时区偏移秒数
device.browser                    -- 浏览器
device.browser_version            -- 浏览器版本
device.web_info                   -- Web信息
```

#### 1.3.6 流量来源字段
```sql
traffic_source.name     -- 流量来源名称
traffic_source.medium   -- 流量来源媒介
traffic_source.source   -- 流量来源
```

#### 1.3.7 事件参数字段（核心）
```sql
event_params            -- 事件参数数组，包含key-value对
  ├── key              -- 参数键名
  └── value            -- 参数值（联合类型）
      ├── string_value -- 字符串值
      ├── int_value    -- 整数值
      ├── float_value  -- 浮点数值
      └── double_value -- 双精度值
```

**事件参数解析示例**：
```sql
-- 解析事件参数的标准方法
SELECT 
    user_pseudo_id,
    event_name,
    MAX(IF(param.key = "action", param.value.int_value, NULL)) AS action,
    MAX(IF(param.key = "ad_platform", param.value.string_value, NULL)) AS ad_platform,
    MAX(IF(param.key = "ad_price", param.value.string_value, NULL)) AS ad_price
FROM `project_id.analytics_app_id.events_*`, 
     UNNEST(event_params) AS param
WHERE (_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')
    AND event_name = 'ad_sdk_price'
    AND param.key IN ("action", "ad_platform", "ad_price")
GROUP BY user_pseudo_id, event_name
```

### 1.4 核心事件定义

#### 1.4.1 用户生命周期事件
```sql
-- 用户首次安装事件
add_his                 -- 新装用户标识事件
add_install            -- 整装用户标识事件

-- 用户活跃度事件
act_1                  -- 主要活跃事件
act_srv_1              -- 服务活跃事件
act_2                  -- 次要活跃事件
```

#### 1.4.2 广告相关事件

**开屏广告事件**：
```sql
-- 普通开屏广告
openscreen_ad_startfun          -- 开屏广告开始
openscreen_ad_request           -- 开屏广告请求
openscreen_ad_return            -- 开屏广告返回
openscreen_ad_ready             -- 开屏广告准备就绪
openscreen_ad_show              -- 开屏广告展示
openscreen_ad_click             -- 开屏广告点击
openscreen_ad_return_primid     -- 开屏广告返回主ID
openscreen_ad_return_backupid   -- 开屏广告返回备用ID
openscreen_ad_timelimt          -- 开屏广告超时

-- 紧急开屏广告
openscreen_emegency_ad_startfun    -- 紧急开屏广告开始
openscreen_emegency_ad_request     -- 紧急开屏广告请求
openscreen_emegency_ad_return      -- 紧急开屏广告返回
openscreen_emegency_ad_ready       -- 紧急开屏广告准备就绪
openscreen_emegency_ad_show        -- 紧急开屏广告展示
openscreen_emegency_ad_click       -- 紧急开屏广告点击
openscreen_emegency_ad_return_primid   -- 紧急开屏广告返回主ID
openscreen_emegency_ad_return_backupid -- 紧急开屏广告返回备用ID
openscreen_emegency_ad_timelimt    -- 紧急开屏广告超时
```

**结果页广告事件**：
```sql
-- 原生广告
result_native_ad_startfun       -- 结果页原生广告开始
result_native_ad_request        -- 结果页原生广告请求
result_native_ad_return         -- 结果页原生广告返回
result_native_ad_ready          -- 结果页原生广告准备就绪
result_native_ad_show           -- 结果页原生广告展示
result_native_ad_click          -- 结果页原生广告点击

-- 插屏广告
result_interstitial_ad_startfun -- 结果页插屏广告开始
result_interstitial_ad_request  -- 结果页插屏广告请求
result_interstitial_ad_return   -- 结果页插屏广告返回
result_interstitial_ad_ready    -- 结果页插屏广告准备就绪
result_interstitial_ad_show     -- 结果页插屏广告展示
result_interstitial_ad_click    -- 结果页插屏广告点击
```

**广告价格事件**：
```sql
ad_sdk_price               -- 广告SDK价格事件
launcher_ad_sdk_price      -- 启动器广告SDK价格事件
kjv_ad_price              -- KJV广告价格事件
ad_scene_price            -- 广告场景价格事件
```

#### 1.4.3 功能使用事件

**清理功能事件**：
```sql
func_pop                  -- 功能弹窗事件
func_homepage            -- 功能首页事件
func_resultpage          -- 功能结果页事件
```

**启动器功能事件**：
```sql
launcher_ad_scene_funnel  -- 启动器广告场景漏斗事件
launcher_theme           -- 启动器主题事件
```

**天气功能事件**：
```sql
weather_noti_pop         -- 天气通知弹窗事件
weather_location         -- 天气位置事件
```

**聊天功能事件**：
```sql
func_chat_role           -- 功能聊天角色事件
```

#### 1.4.4 媒体来源事件
```sql
report_media_source      -- 媒体来源报告事件
```

#### 1.4.5 转化漏斗事件
```sql
ad_conversion_funnel     -- 广告转化漏斗事件
ad_scene_funnel         -- 广告场景漏斗事件
ad_scene_conversion     -- 广告场景转化事件
```

### 1.5 常用事件参数定义

#### 1.5.1 广告相关参数
```sql
-- 广告行为参数
action                  -- 广告行为类型 (1:展示, 2:点击, 3:关闭等)
scene                   -- 广告场景ID
ad_platform            -- 广告平台 (string)
ad_price               -- 广告价格 (string, 单位：微分)
ad_currency            -- 广告货币 (string, 如："USD")
ad_paytype             -- 广告付费类型 (int)
ad_posid               -- 广告位置ID (string)
ad_mediation           -- 广告中介 (int)
ad_category            -- 广告类别 (string)
ad_type                -- 广告类型 (int: 1=插屏, 2=原生等)
ad_place               -- 广告位置 (int)
ad_return              -- 广告返回状态 (int)
return_ad              -- 返回广告状态 (int)

-- 广告标识参数
campaign_id            -- 广告活动ID (string)
if_standbyid           -- 是否备用ID (int: 1=主ID, 2=备用ID, 3=异步主ID)
if_repeat              -- 是否重复 (int)
click_repeat           -- 点击重复 (int)
cooling_period         -- 冷却期 (int)
```

#### 1.5.2 功能使用参数
```sql
-- 功能操作参数
function               -- 功能类型 (int)
type_fix              -- 修复类型 (int)
type_noti             -- 通知类型 (int)
pop_type              -- 弹窗类型 (int)
scenes_type           -- 场景类型 (int)
source                -- 来源 (int)
time                  -- 时间 (string, 毫秒)
event_type            -- 事件类型 (int)

-- 聊天功能参数
Role                  -- 角色 (int)
voice                 -- 语音 (int)
callup                -- 呼叫 (int)

-- 主题功能参数
cate                  -- 类别 (string)
name                  -- 名称 (string)
```

#### 1.5.3 媒体来源参数
```sql
mediasource           -- 媒体来源 (string)
campaign              -- 广告活动 (string)
campaign_id           -- 广告活动ID (string)
```

### 1.6 数据查询模式

#### 1.6.1 用户去重查询
```sql
-- 使用ROW_NUMBER()进行用户去重
SELECT T.user_pseudo_id, T.app_info.version, T.geo.country
FROM (
    SELECT ROW_NUMBER() OVER (
        PARTITION BY user_pseudo_id 
        ORDER BY event_timestamp ASC
    ) AS RNUM, *
    FROM `project_id.analytics_app_id.events_*`
    WHERE (_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')
        AND event_name='add_his'
) AS T
WHERE T.RNUM = 1
```

#### 1.6.2 时间范围查询
```sql
-- 查询特定时间范围的数据
SELECT *
FROM `project_id.analytics_app_id.events_*`
WHERE REGEXP_EXTRACT(_TABLE_SUFFIX, r'[0-9]+') BETWEEN '20231201' AND '20231207'
    AND event_name IN ('act_1', 'act_srv_1')
```

#### 1.6.3 事件参数聚合查询
```sql
-- 聚合事件参数统计
SELECT 
    event_date,
    app_info.version,
    geo.country,
    COUNT(DISTINCT user_pseudo_id) as uv,
    COUNT(*) as pv,
    SUM(IF(param.key = "action" AND param.value.int_value = 1, 1, 0)) as show_pv,
    SUM(IF(param.key = "action" AND param.value.int_value = 2, 1, 0)) as click_pv
FROM `project_id.analytics_app_id.events_*`,
     UNNEST(event_params) AS param
WHERE (_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')
    AND event_name = 'openscreen_ad_show'
    AND param.key = "action"
GROUP BY event_date, app_info.version, geo.country
```

### 1.7 数据质量和特点

#### 1.7.1 数据完整性
- **实时数据**：`events_intraday_*` 表包含当日实时数据，可能不完整
- **完整数据**：`events_*` 表包含当日完整数据，通常在次日凌晨生成
- **数据延迟**：实时数据延迟几分钟到几小时，完整数据延迟12-24小时

#### 1.7.2 数据特征
- **事件驱动**：所有用户行为都以事件形式记录
- **参数化**：事件的具体信息通过参数传递
- **用户匿名**：使用`user_pseudo_id`保护用户隐私
- **时区处理**：所有时间戳都是UTC时间

#### 1.7.3 数据限制
- **采样限制**：大流量应用可能存在数据采样
- **存储限制**：事件参数数量和大小有限制
- **查询限制**：BigQuery查询有时间和资源限制

### 1.8 项目和应用映射

#### 1.8.1 项目配置示例
```php
// 项目ID到应用ID的映射关系
$project_mappings = [
    'longwu-3965' => [
        'app_id' => '307936324',
        'description' => '测试环境',
        'auth_file' => 'longwu-3965-881c4affeafd.json'
    ],
    'phonecleaner-51870' => [
        'app_id' => '314732337', 
        'description' => '清理应用生产环境',
        'auth_file' => 'phonecleaner-51870-xxx.json'
    ],
    'acecleaner-b2224' => [
        'app_id' => '332305483',
        'description' => 'Ace清理应用',
        'auth_file' => 'acecleaner-b2224-b7a560f5fa18.json'
    ]
];
```

#### 1.8.2 应用类型分类
```php
// 应用类型定义
const BQ_TYPE_V2 = 'v2';      // 自营清理类应用
const BQ_TYPE_V3 = 'v3';      // 自营个性化类应用  
const BQ_TYPE_V4 = 'v4';      // 合作猎户类应用
const BQ_TYPE_V7 = 'v7';      // 自营天气类应用
```

## 2. 核心服务类

### 1.1 BigQueryBaseService - 基础服务类

**文件位置**: `app/Services/BigQuery/BigQueryBaseService.php`

**核心功能**: BigQuery查询的基础服务类，提供数据查询、任务管理、数据导入等核心功能

**主要常量**:
```php
const FollowTaskTypeByNoHandle = 0; // 不处理
const FollowTaskTypeByMysql = 1;    // 入mysql
```

**核心方法**:

#### 1.1.1 构造函数
```php
public function __construct()
{
    $this->task_service = new BigQueryTaskService();
}
```

#### 1.1.2 并发控制
```php
public function concurrenceChecker($key, $ct = 5, $second = 10)
{
    $count = Cache::store('database')->get($key, 0);
    Cache::store('database')->put($key, $count + 1, $second);
    $locked = true;
    while ($locked) {
        if ($count < $ct) {
            $locked = false;
        } else {
            sleep($second);
            $locked = false;
        }
    }
}
```

#### 1.1.3 批量任务处理
```php
public function bitchHandle($signature, $task_info, $app_id, $is_force = 0)
{
    $this->concurrenceChecker('big_query_handle', 5, 20);
    $task_info = $this->formatTaskInfo($task_info, $app_id);
    $project_id = $task_info['project_id'];
    $this->data_date = $task_info['data_date'];
    $this->signature = $signature;
    
    // 检查是否已经成功执行
    if (!$is_force) {
        $haing_success_res = $this->task_service->havingSuccessTask($this->data_date, $project_id, $task_info['key']);
        if ($haing_success_res) {
            Log::info($this->signature . '|' . $task_info['key'] . ':当日已经成功获取，如果需要重跑，需要强制执行～～～');
            return false;
        }
    }
    
    // 执行任务列表
    if (!$this->task_service->havingRunTask($this->data_date, $project_id, $task_info['key'])) {
        $fid = $this->task_service->addTaskRecord($this->data_date, $project_id, $task_info['key'], '', $task_info['desc']);
        $tasks = $task_info['list'];
        $responses = $return = [];
        $status = BigQueryTaskService::StatusSuccess;
        
        foreach ($tasks as $k => $task) {
            $res = $this->execute($task, $project_id, $app_id, $fid, $is_force);
            $task_status = $res ? BigQueryTaskService::StatusSuccess : BigQueryTaskService::StatusFail;
            if ($task_status == BigQueryTaskService::StatusFail) $status = BigQueryTaskService::StatusFail;
            
            $response = [
                'key' => $task['key'],
                'task_status' => $task_status,
            ];
            $responses[] = $response;
            $response['res'] = $res;
            $return[] = $response;
        }
        
        $this->task_service->updateTaskRecord($fid, ['status' => $status, 'response' => json_encode($responses)]);
        return $return;
    }
}
```

#### 1.1.4 单任务处理
```php
public function handle($signature, $task_info, $app_id, $is_force = 0)
{
    $task_info['key'] = $app_id . '_' . $task_info['key'];
    $this->data_date = $task_info['data_date'];
    $project_id = $task_info['project_id'];
    $this->signature = $signature;
    
    if (!$is_force) {
        $haing_success_res = $this->task_service->havingSuccessTask($this->data_date, $project_id, $task_info['key']);
        if ($haing_success_res) {
            Log::info($this->signature . ':当日已经成功获取，如果需要重跑，需要强制执行～～～');
            return false;
        }
    }
    
    if (!$this->task_service->havingRunTask($this->data_date, $project_id, $task_info['key'])) {
        return $this->execute($task_info, $project_id, $app_id);
    }
}
```

#### 1.1.5 任务执行
```php
public function execute($task, $project_id, $app_id, $fid = 0, $is_force = 0)
{
    $having_task = $this->task_service->havingRunTask($this->data_date, $project_id, $task['key']);
    if ($having_task) {
        echo $task['key'] . '有任务正在执行：' . $having_task->version;
        Log::info($this->signature . '|' . $task['key'] . ':有任务正在执行,请查看：' . $having_task->version);
        return false;
    }
    
    $version = $this->task_service->addTaskRecord($this->data_date, $project_id, $task['key'], $task['sql'], $task['desc'], $fid);
    
    try {
        $return = $this->query($task, $project_id, $version, $app_id);
    } catch (\Exception $e) {
        Log::error($this->signature . '|' . $version . ':任务失败：' . $e->getLine() . $e->getMessage());
        $this->task_service->updateTaskRecord($version, ['status' => BigQueryTaskService::StatusFail, 'response' => $e->getMessage()]);
        return false;
    }

    if (!$return) return false;
    
    $follow_task_type = $task['follow_task_type'];
    switch ($follow_task_type) {
        case self::FollowTaskTypeByMysql:
            $res = $this->insertDataToMysql($return['version'], $return['result'], $task, $is_force);
            break;
        default:
            $res = $return['result'];
            break;
    }
    
    if ($res) $this->task_service->updateTaskRecord($return['version'], ['status' => BigQueryTaskService::StatusSuccess, 'response' => 'success']);
    return $res;
}
```

#### 1.1.6 BigQuery查询
```php
public function query($task, $project_id, $version, $app_id)
{
    Log::info($this->signature . ':查询BigQuery开始～～～');
    
    if (!$this->check($project_id, $version, $app_id)) return false;
    
    $bigQuery = new BigQueryClient([
        'keyFilePath' => $this->file_path,
        'projectId' => $project_id,
    ]);
    
    $jobConfig = $bigQuery->query($task['sql']);
    $job = $bigQuery->startQuery($jobConfig);
    
    $backoff = new ExponentialBackoff(10);
    $backoff->execute(function () use ($job, $version) {
        Log::info($this->signature . '|' . $job->id() . ':Waiting for job to complete');
        $this->task_service->updateTaskRecord($version, ['status' => BigQueryTaskService::StatusQueryStart, 'job_id' => $job->id()]);
        $job->reload();
        
        if (!$job->isComplete()) {
            $message = 'Job has not yet completed';
            $this->task_service->updateTaskRecord($version, ['status' => BigQueryTaskService::StatusFail, 'response' => $message]);
            Log::error($this->signature . '|' . $job->id() . ':' . $message);
            throw new \Exception($job->id() . ':' . $message, 500);
        }
    });

    $queryResults = $job->queryResults();
    $info = $queryResults->info();
    $bytes = $info['totalBytesProcessed'];
    $count = $info['totalRows'];
    
    $this->task_service->updateTaskRecord($version, ['status' => BigQueryTaskService::StatusQuerySuccess, 'total_bytes' => $bytes, 'count' => $count]);
    
    $return = [];
    $return['version'] = $version;
    $return['result'] = $queryResults;
    
    Log::info($this->signature . ':查询BigQuery结束～～～');
    return $return;
}
```

#### 1.1.7 权限验证
```php
public function check($project_id, $version, $app_id)
{
    $app_service = new AppsService();
    $auth = $app_service->getBQAppsByProjectIdAndAppIdAndMachine($project_id, $app_id);
    
    if (!$auth) {
        $message = 'sorry,this service ip' . $auth->ip . ' can not query bigQuery:APP_ID:' . $app_id;
        $this->task_service->updateTaskRecord($version, ['status' => BigQueryTaskService::StatusFail, 'response' => $message]);
        echo $message;
        Log::error($this->signature . ':' . $message);
        return false;
    }
    
    $this->file_path = $auth->file_path;
    return true;
}
```

#### 1.1.8 数据导入MySQL
```php
public function insertDataToMysql($version, $queryResults, $task, $is_force)
{
    try {
        $this->task_service->updateTaskRecord($version, ['status' => BigQueryTaskService::StatusFollowStart, 'response' => '导入MYSQL开始～～～']);
        
        $can_batch = isset($task['can_batch']) ? $task['can_batch'] : false;
        $col_map = isset($task['col_map']) ? $task['col_map'] : [];
        $table_info = $task['table_info'];
        
        Log::info($this->signature . $version . ':导入MYSQL开始～～～');
        
        if ($can_batch && !$is_force) {
            $this->batchInsertToMysql($queryResults, $table_info, $col_map);
        } else {
            $this->onlyInsertToMysql($queryResults, $table_info, $col_map);
        }
        
        $status = BigQueryTaskService::StatusFollowSuccess;
        $message = '导入 MYSQL 成功';
    } catch (\Exception $e) {
        Log::error($this->signature . $version . ':导入MYSQL失败：' . $e->getMessage());
        $message = ':导入MYSQL失败：' . $e->getMessage();
        $status = BigQueryTaskService::StatusFail;
        $this->task_service->updateTaskRecord($version, ['status' => $status, 'response' => $message]);
        return false;
    }
    
    $this->task_service->updateTaskRecord($version, ['status' => $status, 'response' => $message]);
    Log::info($this->signature . $version . ':导入MYSQL结束～～～');
    return true;
}
```

#### 1.1.9 批量导入MySQL
```php
private function batchInsertToMysql($queryResults, $table_info, $col_map)
{
    $datas = [];
    $ct = 0;
    
    foreach ($queryResults as $row) {
        $data = [];
        if ($col_map) {
            foreach ($col_map as $new_field => $field) {
                $data[$new_field] = $row[$field];
            }
        } else {
            $data = $row;
        }
        
        $datas[] = $data;
        
        if (count($datas) == 500) {
            DB::table($table_info['table_name'])->insert($datas);
            $ct++;
            Log::info($this->signature . '｜第' . $ct . '次导入MYSQL,数量：' . count($datas));
            $datas = [];
        }
    }
    
    if (count($datas) > 0) {
        DB::table($table_info['table_name'])->insert($datas);
        $ct++;
        Log::info($this->signature . '｜第' . $ct . '次导入MYSQL,数量：' . count($datas));
    }
}
```

#### 1.1.10 单条导入MySQL
```php
private function onlyInsertToMysql($queryResults, $table_info, $col_map)
{
    foreach ($queryResults as $row) {
        $data = [];
        if ($col_map) {
            foreach ($col_map as $new_field => $field) {
                $data[$new_field] = $row[$field];
            }
        } else {
            $data = $row;
        }
        
        $uniq_keys = [];
        foreach ($table_info['uniq_key'] as $uniq_key) {
            $uniq_keys[$uniq_key] = $data[$uniq_key];
        }
        
        DB::table($table_info['table_name'])->updateOrInsert($uniq_keys, $data);
    }
}
```

### 1.2 BigQueryTaskService - 任务管理服务

**文件位置**: `app/Services/BigQuery/BigQueryTaskService.php`

**核心功能**: 管理BigQuery任务的生命周期，包括任务创建、状态更新、任务检查等

**任务状态常量**:
```php
const StatusStart = 0;          // 任务开始
const StatusQueryStart = 1;     // bigQuery 查询开始
const StatusQuerySuccess = 2;   // bigQuery 查询成功
const StatusFail = 3;           // 任务失败
const StatusSuccess = 4;        // 任务成功
const StatusFollowStart = 5;    // 后续任务开始
const StatusFollowSuccess = 6;  // 后续任务成功
```

**主要方法**:

#### 1.2.1 获取任务
```php
public function getTaskByVersion($version)
{
    return DB::table('s_big_query_task')->where('version', $version)->first();
}
```

#### 1.2.2 检查运行中的任务
```php
public function havingRunTask($date, $project_id, $title)
{
    return DB::table('s_big_query_task')
        ->where('project_id', $project_id)
        ->where('title', $title)
        ->where('date', $date)
        ->whereNotIn('status', [self::StatusSuccess, self::StatusFail])
        ->first();
}
```

#### 1.2.3 检查成功的任务
```php
public function havingSuccessTask($date, $project_id, $title)
{
    return DB::table('s_big_query_task')
        ->where('project_id', $project_id)
        ->where('title', $title)
        ->where('date', $date)
        ->where('status', self::StatusSuccess)
        ->first();
}
```

#### 1.2.4 添加任务记录
```php
public function addTaskRecord($date, $project_id, $title, $sql, $desc, $fid = 0)
{
    // 生成任务版本号
    $version = date('YmdHis') . rand(10000, 99999);
    if(DB::table('s_big_query_task')->where('version',$version)->exists()){
        sleep(1);
        $version = date('YmdHis') . rand(10000, 99999);
    }
    
    $md5 = md5($sql);
    $insert = [
        'date' => $date,
        'project_id' => $project_id,
        'version' => $version,
        'title' => $title,
        'sql' => $sql ?: "",
        'md5' => $md5,
        'desc' => $desc,
        'status' => self::StatusStart,
        'fid' => $fid
    ];
    
    DB::table('s_big_query_task')->insert($insert);
    return $version;
}
```

#### 1.2.5 更新任务记录
```php
public function updateTaskRecord($version, $update)
{
    DB::table('s_big_query_task')->where('version', $version)->update($update);
}
```

## 2. 数据处理服务类

### 2.1 DataHandleService - 通用数据处理服务

**文件位置**: `app/Services/BigQuery/DataHandleService.php`

**核心功能**: 处理BigQuery查询结果，转换为业务报表数据

**主要场景事件配置**:
```php
private static $BUSINESS_SCENE_EVENTS = [
    DictUtil::SCENE_OPEN_SCREEN => [
        'openscreen_ad_startfun', 'openscreen_ad_request', 'openscreen_ad_return', 
        'openscreen_ad_ready', 'openscreen_ad_show', 'openscreen_ad_click', 
        'openscreen_ad_return_primid', 'openscreen_ad_return_backupid', 'openscreen_ad_timelimt'
    ],
    // ... 其他场景配置
];
```

**主要方法**:

#### 2.1.1 用户核心功能报表
```php
public function set_user_core_func_report($key, $events, $user_type, $item_uv)
{
    $report = [];
    $report['date'] = $key['date'];
    $report['app_id'] = $key['app_id'];
    $report['app_version'] = $key['app_version'];
    $report['country'] = $key['country'];
    $report['user_type'] = $user_type;
    $report['act_srv_uv'] = $item_uv['act_srv_uv'];
    $report['act_main_uv'] = $item_uv['act_main_uv'];
    
    if ($report['act_srv_uv']) {
        // 推送展示相关事件
        $push_show_arr = ['clean_push_show', 'clean_push2_show', 'clean2_push2_show', 'boost_push_show', 'power_push_show'];
        $report['push_show_pv'] = $this->sumPv($push_show_arr, $events);
        $report['push_show_avg'] = $report['push_show_pv'] / $report['act_srv_uv'];
        
        // 推送点击相关事件
        $push_click_arr = ['clean_push_click', 'clean_push2_click', 'clean2_push2_click', 'boost_push_click', 'power_push_click'];
        $report['push_click_pv'] = $this->sumPv($push_click_arr, $events);
        $report['push_click_avg'] = $report['push_click_pv'] / $report['act_srv_uv'];
        $report['push_click_ratio'] = $report['push_show_pv'] ? $report['push_click_pv'] / $report['push_show_pv'] : 0;
    }
    
    // 存储到数据库
    DB::table('s_report_func_scene')->updateOrInsert(
        ['date' => $report['date'], 'app_id' => $report['app_id'], 'app_version' => $report['app_version'],
         'country' => $report['country'], 'user_type' => $report['user_type']],
        $report
    );
}
```

#### 2.1.2 三维报表处理
```php
public function set_three_dimensional_report($key, $type_events, $user)
{
    $report = [];
    $report['date'] = $key['date'];
    $report['app_id'] = $key['app_id'];
    $report['app_version'] = $key['app_version'];
    $report['country'] = $key['country'];
    $report['dnu'] = $user['dnu'];
    $report['act_srv_uv'] = $user['act_srv_uv'];
    $report['act_main_uv'] = $user['act_main_uv'];
    
    if (isset($type_events[DictUtil::USER_TYPE_ACTIVE_USER])) {
        $events_all = array_keys($type_events[DictUtil::USER_TYPE_ACTIVE_USER]);
        $report['act_srv_pv'] = $this->sumPv($events_all, $type_events[DictUtil::USER_TYPE_ACTIVE_USER]);
        $report['act_main_pv'] = isset($type_events[DictUtil::USER_TYPE_ACTIVE_USER]['act_1']) ?
            $type_events[DictUtil::USER_TYPE_ACTIVE_USER]['act_1'] : 0;
        $report['act_srv_avg'] = $report['act_srv_uv'] ? $report['act_srv_pv'] / $report['act_srv_uv'] : 0;
        $report['act_main_avg'] = $report['act_main_uv'] ? $report['act_main_pv'] / $report['act_main_uv'] : 0;
        $report['act_main_ratio'] = $report['act_srv_uv'] ? $report['act_main_uv'] / $report['act_srv_uv'] : 0;
        $report['dnu_ratio'] = $report['act_srv_uv'] ? $report['dnu'] / $report['act_srv_uv'] : 0;
        
        DB::table('s_report_three_dimensional')->updateOrInsert(
            ['date' => $report['date'], 'app_id' => $report['app_id'], 'app_version' => $report['app_version'],
             'country' => $report['country']],
            $report
        );
    }
}
```

#### 2.1.3 商业场景报表
```php
public function set_business_scene_report($key, $events, $user_type, $user)
{
    $report = [];
    $report['date'] = $key['date'];
    $report['app_id'] = $key['app_id'];
    $report['app_version'] = $key['app_version'];
    $report['country'] = $key['country'];
    $report['user_type'] = $user_type;
    
    if (in_array($user_type, [DictUtil::USER_TYPE_NEW_USER, DictUtil::USER_TYPE_NEW_USER_2,
        DictUtil::USER_TYPE_NEW_USER_3, DictUtil::USER_TYPE_ACTIVE_USER])) {
        $report['dnu'] = $user['dnu'];
        $report['dnu_1'] = $user['dnu_1'];
        $report['dnu_2'] = $user['dnu_2'];
    } else {
        $report['dnu'] = $user['install_dnu'];
        $report['dnu_1'] = $user['install_dnu_1'];
        $report['dnu_2'] = $user['install_dnu_2'];
    }
    
    $report['act_srv_uv'] = $user['act_srv_uv'];
    $report['dnu_ratio'] = $report['act_srv_uv'] ? $report['dnu'] / $report['act_srv_uv'] : 0;
    $report['ad_return_async_primid_pv'] = 0;

    // 处理四个商业场景
    $scene_list = [DictUtil::SCENE_OPEN_SCREEN, DictUtil::SCENE_OPEN_SCREEN_EMEGENCY,
        DictUtil::SCENE_RESULT_NATIVE, DictUtil::SCENE_RESULT_INTERSTITIAL,
        DictUtil::SCENE_RESULT_NATIVE_COLD, DictUtil::SCENE_RESULT_INTERSTITIAL_COLD,
        DictUtil::SCENE_RESULT_NATIVE_SUM, DictUtil::SCENE_RESULT_INTERSTITIAL_SUM,
        DictUtil::SCENE_OPEN_SCREEN_SUM
    ];
    
    $main_report = $report;
    foreach ($scene_list as $scene) {
        $report = $main_report;
        // 处理每个场景的具体逻辑
        // ... 场景处理代码
    }
}
```

#### 2.1.4 字典数据设置
```php
public function set_dict_data($date)
{
    // 处理APP版本
    $app_versions = DB::table('s_big_query_base_uv')->where('date', $date)->distinct()->pluck('app_version');
    foreach ($app_versions as $app_version) {
        if ($app_version && $app_version != 'All' && DB::table('s_dict_common')->where('type', 'app_version')->where('key', $app_version)->doesntExist()) {
            $order = DB::table('s_dict_common')->where('type', 'app_version')->max('order');
            DB::table('s_dict_common')->insert([
                'key' => $app_version, 
                'value' => $app_version, 
                'desc' => 'APP版本号', 
                'type' => 'app_version', 
                'order' => $order + 1, 
                'status' => 1
            ]);
        }
    }
    
    // 处理国家数据
    $countrys = DB::table('s_big_query_base_uv')->where('date', $date)->distinct()->pluck('country');
    foreach ($countrys as $country) {
        if ($country && $country != 'All' && DB::table('s_dict_common')->where('type', 'country')->where('key', $country)->doesntExist()) {
            DB::table('s_dict_common')->insert([
                'key' => $country, 
                'value' => $country, 
                'desc' => '国家', 
                'type' => 'country', 
                'status' => 1
            ]);
        }
    }
    
    // 处理Android版本
    $android_versions = DB::table('s_report_dnu_retention')->where('date', date('Ymd', strtotime($date) - 3600 * 24))->distinct()->pluck('android_version');
    foreach ($android_versions as $android_version) {
        if ($android_version && $android_version != 'ALL' && DB::table('s_dict_common')->where('type', 'android_version')->where('key', $android_version)->doesntExist()) {
            DB::table('s_dict_common')->insert([
                'key' => $android_version, 
                'value' => $android_version, 
                'desc' => '安卓版本', 
                'type' => 'android_version', 
                'status' => 1
            ]);
        }
    }
}
```

### 2.2 专业数据处理服务类

#### 2.2.1 DataHandleSelfBusinessService - 自营业务数据处理
**文件位置**: `app/Services/BigQuery/DataHandleSelfBusinessService.php`

#### 2.2.2 DataHandleSelfIndividuationBusinessService - 自营个性化业务数据处理
**文件位置**: `app/Services/BigQuery/DataHandleSelfIndividuationBusinessService.php`

#### 2.2.3 DataHandleSelfService - 自营数据处理
**文件位置**: `app/Services/BigQuery/DataHandleSelfService.php`

#### 2.2.4 CooperationOrion/DataHandleBusinessService - 合作猎户业务数据处理
**文件位置**: `app/Services/BigQuery/CooperationOrion/DataHandleBusinessService.php`

#### 2.2.5 SelfWeather/DataHandleSelfWeatherBusinessService - 自营天气业务数据处理
**文件位置**: `app/Services/BigQuery/SelfWeather/DataHandleSelfWeatherBusinessService.php`

## 3. 命令行类

### 3.1 BigQueryBaseCommand - 基础命令类

**文件位置**: `app/Console/BigQueryBaseCommand.php`

**核心功能**: 为所有BigQuery命令提供基础功能

**主要方法**:
```php
public function __construct()
{
    $this->apps = AppsService::getBQAppsByMachine();
    parent::__construct();
}

public function initializations($method)
{
    $this->signatureWithIp();
    $this->initialization($method);
}

private function getProjectIds()
{
    $ip = get_server_ip();
    if (app()->environment() == 'alpha') $ip = '************';
    return DB::table('s_big_query_auth')
        ->where('ip', $ip)
        ->where('status', BasicUtil::STATUS_ONLINE)
        ->pluck('project_id')
        ->toArray();
}
```

### 3.2 具体命令类

#### 3.2.1 BaseUv - 基础用户数据查询
**文件位置**: `app/Console/Commands/BigQuery/BaseUv.php`

**命令签名**: `big_query:base_uv {force=0} {date=0}`

**功能**: 查询BigQuery基础用户数据

**任务组配置**:
```php
public $task_group_list = [
    [
        'key' => '',
        'desc' => '各种版本和国家',
        'group_by' => 'group by app_version,country',
        'select' => 'app_version,country'
    ],
    [
        'key' => '_ALL_COUNTRY',
        'desc' => '所有国家不同版本',
        'group_by' => 'group by app_version',
        'select' => "app_version,'All' country"
    ],
    [
        'key' => '_ALL_VERSION',
        'desc' => '所有版本不同国家',
        'group_by' => 'group by country',
        'select' => "'All' app_version,country"
    ],
    [
        'key' => '_ALL_ALL',
        'desc' => '所有国家所有版本',
        'group_by' => '',
        'select' => "'All' app_version,'All' country"
    ],
];
```

#### 3.2.2 DictData - 字典数据处理
**文件位置**: `app/Console/Commands/BigQuery/DictData.php`

**命令签名**: `big_query:dict_data {date=0}`

**功能**: 汇总字典数据

#### 3.2.3 批量处理命令

##### SelfClean/QueryBatch - 自营清理批量处理
**文件位置**: `app/Console/Commands/BigQuery/SelfClean/QueryBatch.php`

**命令签名**: `big_query:self:clean:batch {start_date=0} {end_date=0} {force=0} {type=0}`

##### CooperationChiBao/QueryBatch - 合作驰豹批量处理
**文件位置**: `app/Console/Commands/BigQuery/CooperationChiBao/QueryBatch.php`

**命令签名**: `big_query:cooperation:chibao:batch {start_date=0} {end_date=0} {force=0}`

##### SelfIndividuation/QueryBatch - 自营个性化批量处理
**文件位置**: `app/Console/Commands/BigQuery/SelfIndividuation/QueryBatch.php`

**命令签名**: `big_query:self:individuation:batch {start_date=0} {end_date=0} {force=0} {type=0}`

##### CooperationOrion/QueryBatch - 合作猎户批量处理
**文件位置**: `app/Console/Commands/BigQuery/CooperationOrion/QueryBatch.php`

**命令签名**: `big_query:cooperation:orion:batch {start_date=0} {end_date=0} {force=0} {type=0}`

#### 3.2.4 数据处理命令

##### SelfClean/DataHandleBusiness - 自营清理业务数据处理
**文件位置**: `app/Console/Commands/BigQuery/SelfClean/DataHandleBusiness.php`

**命令签名**: `data_handle:self:clean:business_data {date=0}`

##### SelfClean/DataHandleFunction - 自营清理功能数据处理
**文件位置**: `app/Console/Commands/BigQuery/SelfClean/DataHandleFunction.php`

**命令签名**: `data_handle:self:clean:function_data {date=0}`

##### SelfIndividuation/DataHandleBusiness - 自营个性化业务数据处理
**文件位置**: `app/Console/Commands/BigQuery/SelfIndividuation/DataHandleBusiness.php`

**命令签名**: `data_handle:self:individuation:business_data {date=0}`

##### CooperationOrion/DataHandleBusiness - 合作猎户业务数据处理
**文件位置**: `app/Console/Commands/BigQuery/CooperationOrion/DataHandleBusiness.php`

**命令签名**: `data_handle:cooperation:orion:business_data {date=0}`

##### SelfWeather/DataHandleFunction - 自营天气功能数据处理
**文件位置**: `app/Console/Commands/BigQuery/SelfWeather/DataHandleFunction.php`

**命令签名**: `data_handle:self:weather:function_data {date=0}`

#### 3.2.5 测试命令

##### Test/QueryBaseX - 基础测试查询
**文件位置**: `app/Console/Commands/BigQuery/Test/QueryBaseX.php`

**命令签名**: `big_query:base_x {force=0} {date=0}`

##### Test/QueryMax - 最大值查询测试
**文件位置**: `app/Console/Commands/BigQuery/Test/QueryMax.php`

**命令签名**: `big_query:max {force=0} {date=0}`

##### Test/QueryTest - 通用测试查询
**文件位置**: `app/Console/Commands/BigQuery/Test/QueryTest.php`

**命令签名**: `big_query:test {date=0}`

##### Test/BasePvExp - 基础PV实验查询
**文件位置**: `app/Console/Commands/BigQuery/Test/BasePvExp.php`

**命令签名**: `big_query:base_pv_exp {force=0} {date=0}`

##### Test/BaseUvExp - 基础UV实验查询
**文件位置**: `app/Console/Commands/BigQuery/Test/BaseUvExp.php`

**命令签名**: `big_query:base_uv_exp {force=0} {date=0}`

## 4. 模型类

### 4.1 BigQuery模型

**文件位置**: `app/Models/Infrastructure/BigQuery.php`

**功能**: BigQuery认证信息的Eloquent模型

```php
class BigQuery extends Model
{
    protected $table = 's_big_query_auth';
    protected $fillable = [
        'app_id', 'project_id', 'file_path', 'mid', 'status'
    ];
}
```

## 5. 控制器类

### 5.1 BigQueryController

**文件位置**: `app/Http/Controllers/Infrastructure/BigQueryController.php`

**功能**: BigQuery认证信息的HTTP接口控制器

**主要方法**:

#### 5.1.1 认证信息创建
```php
public function auth(Request $request)
{
    $rules = [
        'app_id' => ['required'],
        'project_id' => ['required'],
        'key_file_name' => ['required'],
        'mid' => ['required'],
    ];
    
    $input = $request->all();
    $validator = Validator::make($input, $rules);
    
    if ($validator->fails()) {
        return $this->responseError(CommonException::ERROR_MESSAGE_PARAM, CommonException::ERROR_CODE_PARAM, $validator->errors());
    }
    
    $app_id = htmlspecialchars(addslashes($input['app_id']));
    $project_id = htmlspecialchars(addslashes($input['project_id']));
    $key_file_name = htmlspecialchars(addslashes($input['key_file_name']));
    $mid = $input['mid'];
    
    BigQuery::create([
        'app_id' => $app_id,
        'project_id' => $project_id,
        'file_path' => $key_file_name,
        'mid' => $mid,
    ]);
    
    return $this->responseData(['message' => '创建成功']);
}
```

## 6. 应用服务类

### 6.1 AppsService

**文件位置**: `app/Services/AppsService.php`

**功能**: 应用和BigQuery绑定关系管理

**主要方法**:

#### 6.1.1 获取机器上的BigQuery应用
```php
public static function getBQAppsByMachine($bq_type = '')
{
    $ip = get_server_ip();
    $model = DB::table('s_apps as a')
        ->leftJoin('s_big_query_auth as b', 'a.mid', '=', 'b.mid')
        ->select('a.mid', 'b.app_id', 'b.project_id', 'a.bq_type', 'b.file_path', 'a.bq_type')
        ->where('a.status', BasicUtil::STATUS_ONLINE)
        ->where('b.status', BasicUtil::STATUS_ONLINE)
        ->where('a.ip', $ip);
        
    if ($bq_type) {
        $model = $model->where('a.bq_type', $bq_type);
    }
    
    return $model->get();
}
```

#### 6.1.2 根据项目ID和应用ID获取BigQuery应用
```php
public function getBQAppsByProjectIdAndAppIdAndMachine($project_id, $app_id)
{
    $ip = get_server_ip();
    return DB::table('s_apps as a')
        ->leftJoin('s_big_query_auth as b', 'a.mid', '=', 'b.mid')
        ->select('a.mid', 'b.app_id', 'b.project_id', 'a.bq_type', 'b.file_path', 'a.ip')
        ->where('a.status', BasicUtil::STATUS_ONLINE)
        ->where('b.status', BasicUtil::STATUS_ONLINE)
        ->where('a.ip', $ip)
        ->where('b.project_id', $project_id)
        ->where('b.app_id', $app_id)
        ->first();
}
```

## 7. Python客户端

### 7.1 BigQuery Python客户端

**文件位置**: `scripts/bq_client.py`

**功能**: Python版本的BigQuery客户端

```python
from google.cloud import bigquery

class Bq(object):
    def __init__(self, auth_json_path: str):
        self.auth_json_path = auth_json_path
        self.client = bigquery.Client.from_service_account_json(self.auth_json_path)
```

## 8. 数据流程总结

### 8.1 数据处理流程

1. **认证配置**: 通过`s_big_query_auth`表配置BigQuery项目认证信息
2. **应用绑定**: 通过`s_apps`表将应用与BigQuery项目绑定
3. **任务执行**: 
   - 命令行触发BigQuery查询任务
   - 任务状态记录在`s_big_query_task`表
   - 使用`BigQueryBaseService`执行具体查询
4. **数据处理**: 
   - 查询结果通过各种`DataHandle`服务处理
   - 转换为业务报表数据存储到MySQL
5. **结果存储**: 处理后的数据存储到各种报表表中

### 8.2 关键配置

- **认证文件**: 存储在`storage/bigQuery/`目录下的JSON文件
- **项目配置**: 通过IP地址和机器绑定确定可用的BigQuery项目
- **任务管理**: 通过版本号和状态管理任务执行
- **数据同步**: 支持强制重跑和增量更新

### 8.3 监控和日志

- **任务状态**: 通过`s_big_query_task`表监控任务执行状态
- **日志记录**: 详细的执行日志记录在Laravel日志系统中
- **错误处理**: 完善的异常处理和错误恢复机制

## 9. 使用示例

### 9.1 执行基础用户数据查询
```bash
php artisan big_query:base_uv 0 20231201
```

### 9.2 批量处理自营清理数据
```bash
php artisan big_query:self:clean:batch 20231201 20231207 0 1
```

### 9.3 处理字典数据
```bash
php artisan big_query:dict_data 20231201
```

### 9.4 数据处理
```bash
php artisan data_handle:self:clean:business_data 20231201
```

## 10. 注意事项

1. **权限管理**: 确保机器IP在`s_big_query_auth`表中有相应的权限配置
2. **任务并发**: 系统有并发控制机制，避免重复执行
3. **数据一致性**: 支持强制重跑来保证数据一致性
4. **错误处理**: 任务失败时会记录详细错误信息
5. **性能优化**: 支持批量插入和单条更新两种数据导入方式 

## 11. 最佳实践与性能优化

### 11.1 查询优化建议

#### 11.1.1 分区表查询优化
```sql
-- 好的做法：明确指定分区
WHERE (_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')

-- 避免：不指定分区的全表扫描
WHERE event_date = '20231201'  -- 这会扫描所有分区

-- 推荐：使用日期范围查询
WHERE REGEXP_EXTRACT(_TABLE_SUFFIX, r'[0-9]+') BETWEEN '20231201' AND '20231207'
```

#### 11.1.2 事件参数查询优化
```sql
-- 好的做法：在UNNEST前过滤事件
SELECT user_pseudo_id, MAX(IF(param.key = "action", param.value.int_value, NULL)) AS action
FROM `project_id.analytics_app_id.events_*`, UNNEST(event_params) AS param
WHERE (_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')
    AND event_name = 'ad_sdk_price'  -- 先过滤事件
    AND param.key IN ("action", "ad_price")  -- 再过滤参数
GROUP BY user_pseudo_id

-- 避免：在UNNEST后过滤
SELECT user_pseudo_id, MAX(IF(param.key = "action", param.value.int_value, NULL)) AS action
FROM `project_id.analytics_app_id.events_*`, UNNEST(event_params) AS param
WHERE (_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')
    AND param.key IN ("action", "ad_price")
    AND event_name = 'ad_sdk_price'  -- 后过滤效率低
GROUP BY user_pseudo_id
```

#### 11.1.3 用户去重优化
```sql
-- 推荐：使用ROW_NUMBER()进行高效去重
SELECT T.user_pseudo_id, T.app_info.version, T.geo.country
FROM (
    SELECT ROW_NUMBER() OVER (
        PARTITION BY user_pseudo_id 
        ORDER BY event_timestamp ASC
    ) AS RNUM, *
    FROM `project_id.analytics_app_id.events_*`
    WHERE (_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')
        AND event_name='add_his'
) AS T
WHERE T.RNUM = 1

-- 避免：使用GROUP BY去重（性能较差）
SELECT user_pseudo_id, MIN(app_info.version), MIN(geo.country)
FROM `project_id.analytics_app_id.events_*`
WHERE (_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')
    AND event_name='add_his'
GROUP BY user_pseudo_id
```

### 11.2 代码优化建议

#### 11.2.1 批量处理优化
```php
// 推荐：使用批量插入
private function batchInsertToMysql($queryResults, $table_info, $col_map)
{
    $datas = [];
    $batchSize = 500;  // 适当的批量大小
    
    foreach ($queryResults as $row) {
        $data = $this->mapRowData($row, $col_map);
        $datas[] = $data;
        
        if (count($datas) >= $batchSize) {
            DB::table($table_info['table_name'])->insert($datas);
            $datas = [];  // 清空数组释放内存
        }
    }
    
    // 处理剩余数据
    if (!empty($datas)) {
        DB::table($table_info['table_name'])->insert($datas);
    }
}
```

#### 11.2.2 内存管理优化
```php
// 推荐：及时释放大对象
public function query($task, $project_id, $version, $app_id)
{
    $bigQuery = new BigQueryClient([
        'keyFilePath' => $this->file_path,
        'projectId' => $project_id,
    ]);
    
    $jobConfig = $bigQuery->query($task['sql']);
    $job = $bigQuery->startQuery($jobConfig);
    
    // 等待任务完成
    $this->waitForJobCompletion($job, $version);
    
    $queryResults = $job->queryResults();
    
    // 及时释放BigQuery连接
    unset($bigQuery, $job);
    
    return [
        'version' => $version,
        'result' => $queryResults
    ];
}
```

#### 11.2.3 错误处理优化
```php
// 推荐：详细的错误处理和重试机制
public function executeWithRetry($task, $project_id, $version, $app_id, $maxRetries = 3)
{
    $attempt = 0;
    
    while ($attempt < $maxRetries) {
        try {
            return $this->query($task, $project_id, $version, $app_id);
        } catch (\Google\Cloud\Core\Exception\ServiceException $e) {
            $attempt++;
            
            if ($e->getCode() == 429) {  // 配额限制
                Log::warning("BigQuery quota exceeded, retrying in " . ($attempt * 60) . " seconds");
                sleep($attempt * 60);  // 指数退避
            } elseif ($e->getCode() >= 500) {  // 服务器错误
                Log::warning("BigQuery server error, retrying: " . $e->getMessage());
                sleep($attempt * 30);
            } else {
                throw $e;  // 客户端错误不重试
            }
        } catch (\Exception $e) {
            Log::error("Unexpected error: " . $e->getMessage());
            throw $e;
        }
    }
    
    throw new \Exception("Max retries exceeded");
}
```

### 11.3 性能监控

#### 11.3.1 查询性能监控
```php
// 添加查询性能监控
public function query($task, $project_id, $version, $app_id)
{
    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);
    
    try {
        $result = $this->executeQuery($task, $project_id, $version, $app_id);
        
        // 记录性能指标
        $this->logPerformanceMetrics([
            'version' => $version,
            'execution_time' => microtime(true) - $startTime,
            'memory_usage' => memory_get_usage(true) - $startMemory,
            'bytes_processed' => $result['info']['totalBytesProcessed'] ?? 0,
            'rows_returned' => $result['info']['totalRows'] ?? 0
        ]);
        
        return $result;
    } catch (\Exception $e) {
        $this->logError($version, $e, microtime(true) - $startTime);
        throw $e;
    }
}
```

#### 11.3.2 系统资源监控
```php
// 监控系统资源使用
public function monitorSystemResources()
{
    $metrics = [
        'memory_usage' => memory_get_usage(true),
        'memory_peak' => memory_get_peak_usage(true),
        'cpu_usage' => sys_getloadavg()[0],
        'disk_usage' => disk_free_space('/'),
        'active_connections' => DB::select('SHOW STATUS LIKE "Threads_connected"')[0]->Value
    ];
    
    // 记录到监控系统
    $this->recordMetrics($metrics);
    
    // 检查资源阈值
    if ($metrics['memory_usage'] > 1024 * 1024 * 1024) {  // 1GB
        Log::warning('High memory usage detected: ' . $metrics['memory_usage']);
    }
}
```

## 12. 故障排查指南

### 12.1 常见错误类型

#### 12.1.1 BigQuery相关错误
```php
// 错误代码对照表
const BIGQUERY_ERROR_CODES = [
    400 => 'Bad Request - SQL语法错误或参数错误',
    401 => 'Unauthorized - 认证失败',
    403 => 'Forbidden - 权限不足或配额超限',
    404 => 'Not Found - 表或数据集不存在',
    409 => 'Conflict - 作业冲突',
    429 => 'Too Many Requests - 请求频率过高',
    500 => 'Internal Server Error - BigQuery内部错误',
    503 => 'Service Unavailable - 服务不可用'
];

// 错误处理示例
public function handleBigQueryError(\Google\Cloud\Core\Exception\ServiceException $e)
{
    $errorCode = $e->getCode();
    $errorMessage = $e->getMessage();
    
    switch ($errorCode) {
        case 400:
            Log::error("SQL语法错误: {$errorMessage}");
            return "请检查SQL语法";
            
        case 403:
            if (strpos($errorMessage, 'quota') !== false) {
                Log::warning("BigQuery配额超限: {$errorMessage}");
                return "配额超限，请稍后重试";
            }
            Log::error("权限不足: {$errorMessage}");
            return "权限不足";
            
        case 404:
            Log::error("表不存在: {$errorMessage}");
            return "数据表不存在";
            
        case 429:
            Log::warning("请求频率过高: {$errorMessage}");
            return "请求频率过高，请稍后重试";
            
        default:
            Log::error("未知BigQuery错误: {$errorCode} - {$errorMessage}");
            return "系统错误，请联系管理员";
    }
}
```

#### 12.1.2 数据库连接错误
```php
// 数据库连接错误处理
public function handleDatabaseError(\Exception $e)
{
    $errorMessage = $e->getMessage();
    
    if (strpos($errorMessage, 'Connection refused') !== false) {
        Log::error("数据库连接被拒绝: {$errorMessage}");
        return "数据库连接失败";
    }
    
    if (strpos($errorMessage, 'Too many connections') !== false) {
        Log::error("数据库连接数过多: {$errorMessage}");
        return "数据库连接数达到上限";
    }
    
    if (strpos($errorMessage, 'Deadlock') !== false) {
        Log::warning("数据库死锁: {$errorMessage}");
        return "数据库死锁，请重试";
    }
    
    Log::error("数据库错误: {$errorMessage}");
    return "数据库操作失败";
}
```

### 12.2 排查步骤

#### 12.2.1 任务执行失败排查
```bash
# 1. 检查任务状态
SELECT * FROM s_big_query_task 
WHERE date = '20231201' 
    AND status = 3  -- 失败状态
ORDER BY created_at DESC;

# 2. 检查错误日志
tail -f storage/logs/laravel.log | grep "BigQuery"

# 3. 检查系统资源
free -h  # 内存使用情况
df -h    # 磁盘使用情况
top      # CPU使用情况

# 4. 检查网络连接
ping googleapis.com
nslookup bigquery.googleapis.com
```

#### 12.2.2 数据不一致排查
```sql
-- 检查数据完整性
SELECT 
    date,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_pseudo_id) as unique_users,
    MIN(created_at) as first_record,
    MAX(created_at) as last_record
FROM s_big_query_base_uv 
WHERE date BETWEEN '20231201' AND '20231207'
GROUP BY date
ORDER BY date;

-- 检查重复数据
SELECT 
    date, app_id, app_version, country, user_type,
    COUNT(*) as count
FROM s_report_business_scene 
WHERE date = '20231201'
GROUP BY date, app_id, app_version, country, user_type
HAVING COUNT(*) > 1;
```

### 12.3 性能问题排查

#### 12.3.1 慢查询分析
```sql
-- 检查慢查询
SELECT 
    version,
    title,
    TIMESTAMPDIFF(SECOND, created_at, updated_at) as duration_seconds,
    total_bytes,
    count,
    status
FROM s_big_query_task 
WHERE date = '20231201'
    AND TIMESTAMPDIFF(SECOND, created_at, updated_at) > 300  -- 超过5分钟
ORDER BY duration_seconds DESC;
```

#### 12.3.2 内存使用分析
```php
// 内存使用分析工具
public function analyzeMemoryUsage()
{
    $memoryUsage = memory_get_usage(true);
    $memoryPeak = memory_get_peak_usage(true);
    
    Log::info("Memory Analysis", [
        'current_usage' => $this->formatBytes($memoryUsage),
        'peak_usage' => $this->formatBytes($memoryPeak),
        'limit' => ini_get('memory_limit')
    ]);
    
    // 检查内存泄漏
    if ($memoryUsage > 512 * 1024 * 1024) {  // 512MB
        Log::warning("High memory usage detected");
        $this->dumpMemoryUsage();
    }
}

private function formatBytes($bytes)
{
    $units = ['B', 'KB', 'MB', 'GB'];
    $i = 0;
    
    while ($bytes >= 1024 && $i < 3) {
        $bytes /= 1024;
        $i++;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}
```

## 13. 常见问题FAQ

### 13.1 数据相关问题

**Q: 为什么BigQuery查询结果和预期不符？**
A: 
1. 检查时间范围是否正确，注意实时数据和完整数据的区别
2. 确认事件参数解析是否正确
3. 检查用户去重逻辑是否正确
4. 验证过滤条件是否合理

**Q: 如何处理数据延迟问题？**
A:
1. 实时数据：使用 `events_intraday_*` 表，延迟几分钟到几小时
2. 完整数据：使用 `events_*` 表，延迟12-24小时
3. 建议同时查询两个表：`(_TABLE_SUFFIX='20231201' OR _TABLE_SUFFIX='intraday_20231201')`

**Q: 如何优化大数据量查询？**
A:
1. 使用分区过滤减少扫描量
2. 在UNNEST前过滤事件和参数
3. 使用ROW_NUMBER()进行高效去重
4. 考虑使用物化视图缓存常用查询

### 13.2 系统相关问题

**Q: 任务执行失败如何处理？**
A:
1. 检查 `s_big_query_task` 表中的错误信息
2. 查看Laravel日志文件
3. 检查BigQuery配额和权限
4. 验证认证文件是否正确

**Q: 如何处理并发问题？**
A:
1. 使用 `concurrenceChecker` 方法控制并发数
2. 检查任务状态避免重复执行
3. 使用数据库锁机制
4. 实现任务队列管理

**Q: 内存不足如何解决？**
A:
1. 增加PHP内存限制
2. 使用批量处理减少内存占用
3. 及时释放大对象
4. 优化查询减少数据量

### 13.3 配置相关问题

**Q: 如何添加新的BigQuery项目？**
A:
1. 在 `s_big_query_auth` 表中添加认证信息
2. 上传对应的JSON认证文件到 `storage/bigQuery/` 目录
3. 在 `s_apps` 表中配置应用和项目的绑定关系
4. 测试连接和权限

**Q: 如何配置不同环境的数据源？**
A:
1. 使用环境变量区分不同环境
2. 在配置文件中设置不同的项目ID和应用ID
3. 使用不同的认证文件
4. 配置不同的数据库连接

## 14. 版本更新日志

### 14.1 主要版本变更

#### v2.0 (自营清理类应用)
- 支持基础清理功能事件
- 实现广告转化漏斗分析
- 添加功能使用统计

#### v3.0 (自营个性化类应用)
- 支持启动器相关事件
- 添加主题和个性化功能
- 实现聊天功能统计

#### v4.0 (合作猎户类应用)
- 支持合作方数据格式
- 添加特定事件处理
- 实现数据格式转换

#### v7.0 (自营天气类应用)
- 支持天气相关事件
- 添加位置和通知功能
- 实现天气数据统计

### 14.2 最新更新
- 添加详细的数据来源文档
- 完善错误处理机制
- 优化查询性能
- 增加监控和告警功能

## 15. 联系方式

**技术支持**: 数据团队
**文档维护**: 开发团队
**更新频率**: 每月更新

---

*本文档最后更新时间: 2024年1月* 