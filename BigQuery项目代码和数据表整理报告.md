# BigQuery项目代码和数据表整理报告

## 1. 项目概述

本项目是一个基于Laravel框架的数据分析系统，主要用于处理来自Google BigQuery的Firebase Analytics数据。系统通过定时任务从BigQuery查询数据，处理后存储到MySQL数据库中，为业务报表和数据分析提供支持。

## 2. 核心架构

### 2.1 数据流向
```
Firebase Analytics → BigQuery → Laravel系统 → MySQL数据库 → 业务报表
```

### 2.2 主要组件
- **BigQuery服务层**: 负责与Google BigQuery交互
- **命令行任务**: 定时执行数据同步和处理
- **数据处理服务**: 将原始数据转换为业务报表数据
- **数据存储**: MySQL数据库存储处理后的数据

## 3. 核心服务类

### 3.1 BigQueryBaseService (基础服务类)
**文件位置**: `app/Services/BigQuery/BigQueryBaseService.php`

**主要功能**:
- BigQuery查询执行
- 任务管理和状态跟踪
- 数据导入MySQL
- 并发控制
- 权限验证

**核心方法**:
- `bitchHandle()`: 批量任务处理
- `handle()`: 单任务处理
- `execute()`: 任务执行
- `query()`: BigQuery查询
- `insertDataToMysql()`: 数据导入MySQL

### 3.2 BigQueryTaskService (任务管理服务)
**文件位置**: `app/Services/BigQuery/BigQueryTaskService.php`

**主要功能**:
- 任务状态管理
- 任务记录的增删改查
- 任务去重检查

**任务状态**:
```php
const StatusStart = 0;          // 任务开始
const StatusQueryStart = 1;     // BigQuery查询开始
const StatusQuerySuccess = 2;   // BigQuery查询成功
const StatusFail = 3;           // 任务失败
const StatusSuccess = 4;        // 任务成功
const StatusFollowStart = 5;    // 后续任务开始
const StatusFollowSuccess = 6;  // 后续任务成功
```

### 3.3 数据处理服务类

#### 3.3.1 DataHandleService (通用数据处理)
**文件位置**: `app/Services/BigQuery/DataHandleService.php`
- 处理基础用户数据
- 生成三维报表
- 处理商业场景报表

#### 3.3.2 专业数据处理服务
- `DataHandleSelfBusinessService`: 自营业务数据处理
- `DataHandleSelfIndividuationBusinessService`: 自营个性化业务数据处理
- `DataHandleSelfService`: 自营数据处理
- `CooperationOrion/DataHandleBusinessService`: 合作猎户业务数据处理
- `SelfWeather/DataHandleSelfWeatherBusinessService`: 自营天气业务数据处理

## 4. 命令行任务系统

### 4.1 基础命令类
**文件位置**: `app/Console/BigQueryBaseCommand.php`

**主要功能**:
- 提供BigQuery命令的基础功能
- 应用配置管理
- 组合查询生成

### 4.2 具体命令类

#### 4.2.1 按业务类型分类

**自营清理类应用 (SelfClean)**:
- `BaseUv.php`: 基础用户数据
- `BasePvBusiness.php`: 商业场景PV数据
- `BasePvFunction.php`: 功能场景PV数据
- `DataHandleBusiness.php`: 商业数据处理
- `DataHandleFunction.php`: 功能数据处理
- `AdData.php`: 广告数据
- `QueryBatch.php`: 批量查询

**自营个性化类应用 (SelfIndividuation)**:
- `BaseUv.php`: 基础用户数据
- `BasePvBusiness.php`: 商业场景PV数据
- `BaseSubscribeUv.php`: 订阅用户数据
- `ChatFuncData.php`: 聊天功能数据
- `LauncherFuncPop.php`: 启动器功能弹窗数据
- `LauncherFuncTopic.php`: 启动器功能主题数据
- `AdsData.php`: 广告数据
- `AdsMediaData.php`: 广告媒体数据
- `RetentionDefault.php`: 留存数据
- `DataHandleBusiness.php`: 商业数据处理
- `DataHandleBusinessAds.php`: 商业广告数据处理
- `DataHandleChatFunc.php`: 聊天功能数据处理
- `DataHandleLauncherFuncPop.php`: 启动器功能弹窗数据处理
- `DataHandleLauncherFuncTopic.php`: 启动器功能主题数据处理
- `DataHandleSubscribe.php`: 订阅数据处理

**合作猎户类应用 (CooperationOrion)**:
- `BasePvBusiness.php`: 商业场景PV数据
- `BasePvFunction.php`: 功能场景PV数据
- `DataHandleBusiness.php`: 商业数据处理
- `DataHandleFunction.php`: 功能数据处理

**合作赤豹类应用 (CooperationChiBao)**:
- `BasePv.php`: 基础PV数据
- `DataHandle.php`: 数据处理
- `QueryBatch.php`: 批量查询

**自营天气类应用 (SelfWeather)**:
- `BasePvBusiness.php`: 商业场景PV数据
- `BasePvFunction.php`: 功能场景PV数据
- `DataHandleBusiness.php`: 商业数据处理
- `DataHandleFunction.php`: 功能数据处理

#### 4.2.2 通用命令
- `BaseUv.php`: 基础用户数据查询
- `BusinessDnuRetention.php`: 业务新用户留存数据
- `DictData.php`: 字典数据处理

## 5. 数据表结构

### 5.1 BigQuery相关核心表

#### 5.1.1 任务管理表
```sql
-- BigQuery任务记录表
s_big_query_task
- id: 主键
- date: 数据日期
- project_id: BigQuery项目ID
- version: 任务版本号
- title: 任务标题
- sql: 执行的SQL语句
- md5: SQL的MD5值
- desc: 任务描述
- status: 任务状态
- fid: 父任务ID
- job_id: BigQuery作业ID
- total_bytes: 处理的字节数
- count: 结果行数
- response: 响应信息
- created_at: 创建时间
- updated_at: 更新时间
```

#### 5.1.2 认证配置表
```sql
-- BigQuery认证配置表
s_big_query_auth
- id: 主键
- app_id: 应用ID
- project_id: BigQuery项目ID
- file_path: 认证文件路径
- mid: 机器ID
- ip: 服务器IP
- status: 状态
```

### 5.2 基础数据表

#### 5.2.1 用户数据表
```sql
-- 基础用户数据表
s_big_query_base_uv
- id: 主键
- date: 数据日期
- app_id: 应用ID
- app_version: 应用版本
- country: 国家
- type: 用户类型
- act_srv_uv: 服务活跃用户数
- act_main_uv: 主要活跃用户数
```

#### 5.2.2 PV数据表
```sql
-- 基础PV数据表 (按业务类型分表)
s_big_query_base_pv_self_business_[date]     -- 自营业务PV数据
s_big_query_base_pv_self_weather_business_[date] -- 自营天气业务PV数据
s_big_query_base_pv_[date]                   -- 通用PV数据

字段结构:
- date: 数据日期
- app_id: 应用ID
- app_version: 应用版本
- country: 国家
- user_type: 用户类型
- event_name: 事件名称
- pv: 页面浏览量
```

#### 5.2.3 订阅数据表
```sql
-- 订阅用户数据表
s_big_query_base_subscribe_uv
- date: 数据日期
- app_id: 应用ID
- type: 类型
- app_version: 应用版本
- country: 国家
- source: 来源
- action: 行为
- uv: 用户数
```

#### 5.2.4 功能数据表
```sql
-- 聊天功能数据表
s_big_query_func_chat
- date: 数据日期
- app_id: 应用ID
- app_version: 应用版本
- country: 国家
- uv_new: 新用户数
- uv_act: 活跃用户数
- uv_def: 默认用户数
- uv_new_def: 新默认用户数
```

### 5.3 报表数据表

#### 5.3.1 功能场景报表
```sql
-- 功能场景报表
s_report_func_scene
- date: 数据日期
- app_id: 应用ID
- app_version: 应用版本
- country: 国家
- user_type: 用户类型
- act_srv_uv: 服务活跃用户数
- act_main_uv: 主要活跃用户数
- push_show_pv: 推送展示PV
- push_show_avg: 推送展示平均值
- push_click_pv: 推送点击PV
- push_click_avg: 推送点击平均值
- push_click_ratio: 推送点击率
- main_click_pv: 主要点击PV
- main_click_avg: 主要点击平均值
- rpage_show_pv: 结果页展示PV
- rpage_show_avg: 结果页展示平均值
```

#### 5.3.2 三维数据报表
```sql
-- 三维数据报表
s_report_three_dimensional
- date: 数据日期
- app_id: 应用ID
- app_version: 应用版本
- country: 国家
- dnu: 新用户数
- act_srv_uv: 服务活跃用户数
- act_main_uv: 主要活跃用户数
- act_srv_pv: 服务活跃PV
- act_main_pv: 主要活跃PV
- act_srv_avg: 服务活跃平均值
- act_main_avg: 主要活跃平均值
- act_main_ratio: 主要活跃比率
- dnu_ratio: 新用户比率
```

#### 5.3.3 商业场景报表
```sql
-- 商业场景报表 (按业务类型分表)
s_report_business_scene              -- 通用商业场景报表
s_report_self_business_scene         -- 自营商业场景报表
s_report_self_individuation_business_scene -- 自营个性化商业场景报表
s_report_cooperation_orion_business_scene   -- 合作猎户商业场景报表
s_report_self_weather_business_scene -- 自营天气商业场景报表

字段结构:
- date: 数据日期
- app_id: 应用ID
- app_version: 应用版本
- country: 国家
- user_type: 用户类型
- scene: 场景类型
- dnu: 新用户数
- act_srv_uv: 服务活跃用户数
- ad_startfun_pv: 广告开始PV
- ad_request_pv: 广告请求PV
- ad_return_pv: 广告返回PV
- ad_ready_pv: 广告准备PV
- ad_show_pv: 广告展示PV
- ad_click_pv: 广告点击PV
- 各种比率和平均值字段...
```

#### 5.3.4 留存数据报表
```sql
-- 新用户留存报表
s_report_dnu_retention
- date: 数据日期
- app_id: 应用ID
- app_version: 应用版本
- country: 国家
- android_version: Android版本
- dnu: 新用户数
- dnu_1: 次日留存用户数
- dnu_2: 第3日留存用户数
- dnu_3: 第4日留存用户数
- dnu_4: 第5日留存用户数
- dnu_5: 第6日留存用户数
- dnu_6: 第7日留存用户数
- retention_1: 次日留存率
- retention_2: 第3日留存率
- ... (其他留存率字段)
```

### 5.4 字典和配置表

#### 5.4.1 应用配置表
```sql
-- 应用配置表
s_apps
- mid: 机器ID
- app_id: 应用ID
- project_id: BigQuery项目ID
- bq_type: BigQuery类型 (v2/v3/v4/v7)
- package_name: 包名
- status: 状态
- ip: 服务器IP
```

#### 5.4.2 字典表
```sql
-- 通用字典表
s_dict_common
- key: 键
- value: 值
- desc: 描述
- type: 类型 (app_version/country/android_version等)
- order: 排序
- status: 状态
```

## 6. BigQuery数据源分析

### 6.1 数据来源
数据主要来源于**Firebase Analytics (Google Analytics for Firebase)**，以事件流形式实时收集用户行为数据。

### 6.2 表命名规则
```
`project_id.analytics_app_id.events_*`
```
- `project_id`: Google Cloud项目ID
- `analytics_app_id`: Firebase Analytics应用ID
- `events_*`: 事件表，支持通配符查询

### 6.3 核心事件类型

#### 6.3.1 用户生命周期事件
- `add_his`: 新装用户标识事件
- `add_install`: 整装用户标识事件
- `act_1`: 主要活跃事件
- `act_srv_1`: 服务活跃事件

#### 6.3.2 广告相关事件
**开屏广告**:
- `openscreen_ad_startfun`: 开屏广告开始
- `openscreen_ad_request`: 开屏广告请求
- `openscreen_ad_return`: 开屏广告返回
- `openscreen_ad_ready`: 开屏广告准备就绪
- `openscreen_ad_show`: 开屏广告展示
- `openscreen_ad_click`: 开屏广告点击

**结果页广告**:
- `result_native_ad_*`: 结果页原生广告系列
- `result_interstitial_ad_*`: 结果页插屏广告系列

**广告价格事件**:
- `ad_sdk_price`: 广告SDK价格事件
- `launcher_ad_sdk_price`: 启动器广告SDK价格事件

#### 6.3.3 功能使用事件
- `func_pop`: 功能弹窗事件
- `func_homepage`: 功能首页事件
- `func_resultpage`: 功能结果页事件
- `launcher_theme`: 启动器主题事件
- `weather_noti_pop`: 天气通知弹窗事件
- `func_chat_role`: 功能聊天角色事件

### 6.4 事件参数结构
```sql
event_params -- 事件参数数组
├── key      -- 参数键名
└── value    -- 参数值（联合类型）
    ├── string_value -- 字符串值
    ├── int_value    -- 整数值
    ├── float_value  -- 浮点数值
    └── double_value -- 双精度值
```

## 7. 应用类型分类

### 7.1 应用类型定义
```php
const BQ_TYPE_V2 = 'v2';  // 自营清理类应用
const BQ_TYPE_V3 = 'v3';  // 自营个性化类应用  
const BQ_TYPE_V4 = 'v4';  // 合作猎户类应用
const BQ_TYPE_V7 = 'v7';  // 自营天气类应用
```

### 7.2 项目配置示例
```php
$project_mappings = [
    'longwu-3965' => [
        'app_id' => '307936324',
        'description' => '测试环境',
        'auth_file' => 'longwu-3965-881c4affeafd.json'
    ],
    'phonecleaner-51870' => [
        'app_id' => '314732337', 
        'description' => '清理应用生产环境',
        'auth_file' => 'phonecleaner-51870-xxx.json'
    ],
    'acecleaner-b2224' => [
        'app_id' => '332305483',
        'description' => 'Ace清理应用',
        'auth_file' => 'acecleaner-b2224-b7a560f5fa18.json'
    ]
];
```

## 8. 数据处理流程

### 8.1 整体流程
1. **认证配置**: 通过`s_big_query_auth`表配置BigQuery项目认证信息
2. **应用绑定**: 通过`s_apps`表将应用与BigQuery项目绑定
3. **任务执行**: 
   - 命令行触发BigQuery查询任务
   - 任务状态记录在`s_big_query_task`表
   - 使用`BigQueryBaseService`执行具体查询
4. **数据处理**: 
   - 查询结果通过各种`DataHandle*Service`处理
   - 转换为业务报表数据存储到MySQL
5. **报表生成**: 基于处理后的数据生成各种业务报表

### 8.2 任务调度
系统通过Laravel的任务调度系统定时执行各种BigQuery数据同步任务，通常按以下频率执行：
- 基础数据: 每日执行
- 业务报表: 每日执行
- 留存数据: 每日执行（需要历史数据）

### 8.3 监控和日志
- **任务状态**: 通过`s_big_query_task`表监控任务执行状态
- **日志记录**: 详细的执行日志记录在Laravel日志系统中
- **错误处理**: 完善的异常处理和错误恢复机制

## 9. 使用示例

### 9.1 执行基础用户数据同步
```bash
php artisan big_query:base_uv 0 20231201
```

### 9.2 执行商业数据处理
```bash
php artisan big_query:self_clean:data_handle_business 0 20231201
```

### 9.3 强制重跑任务
```bash
php artisan big_query:base_uv 1 20231201
```

## 10. 注意事项

1. **权限管理**: 确保机器IP在`s_big_query_auth`表中有相应的权限配置
2. **任务并发**: 系统有并发控制机制，避免重复执行
3. **数据一致性**: 支持强制重跑来保证数据一致性
4. **资源消耗**: BigQuery查询会消耗配额，需要合理控制查询频率
5. **数据延迟**: 实时数据可能不完整，完整数据通常延迟12-24小时

## 11. 维护和监控

### 11.1 常用监控SQL
```sql
-- 检查任务状态
SELECT * FROM s_big_query_task 
WHERE date = '20231201' 
    AND status = 3  -- 失败状态
ORDER BY created_at DESC;

-- 检查数据完整性
SELECT 
    date,
    COUNT(*) as record_count,
    MIN(created_at) as first_record,
    MAX(created_at) as last_record
FROM s_big_query_base_uv 
WHERE date BETWEEN '20231201' AND '20231207'
GROUP BY date
ORDER BY date;

-- 检查长时间运行的任务
SELECT 
    version,
    title,
    status,
    TIMESTAMPDIFF(SECOND, created_at, updated_at) as duration_seconds,
    total_bytes,
    count,
    status
FROM s_big_query_task 
WHERE date = '20231201'
    AND TIMESTAMPDIFF(SECOND, created_at, updated_at) > 300  -- 超过5分钟
ORDER BY duration_seconds DESC;
```

### 11.2 故障排查

**Q: 任务执行失败如何处理？**
A:
1. 检查 `s_big_query_task` 表中的错误信息
2. 查看Laravel日志文件
3. 检查BigQuery配额和权限
4. 验证SQL语句的正确性
5. 检查网络连接和认证文件

**Q: 数据不一致如何处理？**
A:
1. 使用强制重跑参数重新执行任务
2. 检查源数据的完整性
3. 验证数据处理逻辑
4. 对比不同时间段的数据

**Q: 如何添加新的BigQuery项目？**
A:
1. 在 `s_big_query_auth` 表中添加认证信息
2. 上传对应的JSON认证文件到 `storage/bigQuery/` 目录
3. 在 `s_apps` 表中配置应用和项目的绑定关系
4. 测试连接和权限

## 12. 总结

本项目构建了一个完整的BigQuery数据处理系统，涵盖了从数据获取、处理、存储到报表生成的全流程。系统具有良好的扩展性和可维护性，支持多种业务类型的数据处理需求。通过合理的架构设计和完善的监控机制，确保了数据处理的准确性和系统的稳定性。