[2025-07-15 12:12:22] local.INFO: 本机IP：**************  
[2025-07-15 12:12:22] local.INFO: **************_business_report:admob {start_date=0} {end_date=0}:任务开始～～～  
[2025-07-15 12:12:23] local.ERROR: **************_business_report:admob {start_date=0} {end_date=0}:任务失败：#0 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Services/Business/Google/GoogleAdmobAuthService.php(34): Google\Client->setAuthConfig()
#1 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Console/Commands/BusinessReport/Admob.php(82): App\Services\Business\Google\GoogleAdmobAuthService->getToken()
#2 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Console/Commands/BusinessReport/Admob.php(60): App\Console\Commands\BusinessReport\Admob->getService()
#3 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\Console\Commands\BusinessReport\Admob->index()
#4 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#5 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure()
#6 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod()
#7 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\Container\BoundMethod::call()
#8 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Console/BaseCommand.php(65): Illuminate\Container\Container->call()
#9 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Console/Commands/BusinessReport/Admob.php(50): App\Console\BaseCommand->initialization()
#10 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\Console\Commands\BusinessReport\Admob->handle()
#11 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#12 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure()
#13 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod()
#14 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\Container\BoundMethod::call()
#15 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\Container\Container->call()
#16 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/symfony/console/Command/Command.php(298): Illuminate\Console\Command->execute()
#17 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\Component\Console\Command\Command->run()
#18 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/symfony/console/Application.php(1040): Illuminate\Console\Command->run()
#19 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/symfony/console/Application.php(301): Symfony\Component\Console\Application->doRunCommand()
#20 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/symfony/console/Application.php(171): Symfony\Component\Console\Application->doRun()
#21 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\Component\Console\Application->run()
#22 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Console\Application->run()
#23 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/artisan(37): Illuminate\Foundation\Console\Kernel->handle()
#24 {main}  
[2025-07-15 12:13:02] local.INFO: 本机IP：**************  
[2025-07-15 12:13:02] local.INFO: **************_business_report:admob {start_date=0} {end_date=0}:任务开始～～～  
[2025-07-15 12:13:04] local.ERROR: **************_business_report:admob {start_date=0} {end_date=0}:任务失败：#0 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/google/apiclient/src/Http/REST.php(107): Google\Http\REST::decodeHttpResponse()
#1 [internal function]: Google\Http\REST::doExecute()
#2 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/google/apiclient/src/Task/Runner.php(187): call_user_func_array()
#3 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/google/apiclient/src/Http/REST.php(66): Google\Task\Runner->run()
#4 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/google/apiclient/src/Client.php(920): Google\Http\REST::execute()
#5 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/google/apiclient/src/Service/Resource.php(238): Google\Client->execute()
#6 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/google/apiclient-services/src/AdMob/Resource/Accounts.php(67): Google\Service\Resource->call()
#7 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Services/Business/Google/GoogleAdmobService.php(27): Google\Service\AdMob\Resource\Accounts->listAccounts()
#8 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Console/Commands/BusinessReport/Admob.php(67): App\Services\Business\Google\GoogleAdmobService->get_accounts()
#9 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Console/Commands/BusinessReport/Admob.php(61): App\Console\Commands\BusinessReport\Admob->grab()
#10 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\Console\Commands\BusinessReport\Admob->index()
#11 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#12 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure()
#13 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod()
#14 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\Container\BoundMethod::call()
#15 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Console/BaseCommand.php(65): Illuminate\Container\Container->call()
#16 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/app/Console/Commands/BusinessReport/Admob.php(50): App\Console\BaseCommand->initialization()
#17 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\Console\Commands\BusinessReport\Admob->handle()
#18 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/Util.php(40): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#19 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure()
#20 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod()
#21 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Container/Container.php(653): Illuminate\Container\BoundMethod::call()
#22 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Console/Command.php(136): Illuminate\Container\Container->call()
#23 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/symfony/console/Command/Command.php(298): Illuminate\Console\Command->execute()
#24 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Console/Command.php(121): Symfony\Component\Console\Command\Command->run()
#25 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/symfony/console/Application.php(1040): Illuminate\Console\Command->run()
#26 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/symfony/console/Application.php(301): Symfony\Component\Console\Application->doRunCommand()
#27 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/symfony/console/Application.php(171): Symfony\Component\Console\Application->doRun()
#28 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Console/Application.php(94): Symfony\Component\Console\Application->run()
#29 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Console\Application->run()
#30 /Users/<USER>/DevTool/project/liebao/swan_admin_oversea/artisan(37): Illuminate\Foundation\Console\Kernel->handle()
#31 {main}  
